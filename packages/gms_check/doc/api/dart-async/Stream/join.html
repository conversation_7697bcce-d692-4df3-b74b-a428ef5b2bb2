<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the join method from the Stream class, for the Dart programming language.">
  <title>join method - Stream class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li><a href="dart-async/Stream-class.html">Stream<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></a></li>
    <li class="self-crumb">join method</li>
  </ol>
  <div class="self-name">join</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li><a href="dart-async/Stream-class.html">Stream<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></a></li>
      <li class="self-crumb">join method</li>
    </ol>
    
    <h5>Stream class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-async/Stream-class.html#constructors">Constructors</a></li>
        <li><a href="dart-async/Stream/Stream.html">Stream</a></li>
        <li><a href="dart-async/Stream/Stream.empty.html">empty</a></li>
        <li><a href="dart-async/Stream/Stream.error.html">error</a></li>
        <li><a href="dart-async/Stream/Stream.eventTransformed.html">eventTransformed</a></li>
        <li><a href="dart-async/Stream/Stream.fromFuture.html">fromFuture</a></li>
        <li><a href="dart-async/Stream/Stream.fromFutures.html">fromFutures</a></li>
        <li><a href="dart-async/Stream/Stream.fromIterable.html">fromIterable</a></li>
        <li><a href="dart-async/Stream/Stream.multi.html">multi</a></li>
        <li><a href="dart-async/Stream/Stream.periodic.html">periodic</a></li>
        <li><a href="dart-async/Stream/Stream.value.html">value</a></li>
    
        <li class="section-title">
            <a href="dart-async/Stream-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-async/Stream/first.html">first</a></li>
        <li><a href="dart-async/Stream/isBroadcast.html">isBroadcast</a></li>
        <li><a href="dart-async/Stream/isEmpty.html">isEmpty</a></li>
        <li><a href="dart-async/Stream/last.html">last</a></li>
        <li><a href="dart-async/Stream/length.html">length</a></li>
        <li><a href="dart-async/Stream/single.html">single</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-async/Stream-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-async/Stream/any.html">any</a></li>
        <li><a href="dart-async/Stream/asBroadcastStream.html">asBroadcastStream</a></li>
        <li><a href="dart-async/Stream/asyncExpand.html">asyncExpand</a></li>
        <li><a href="dart-async/Stream/asyncMap.html">asyncMap</a></li>
        <li><a href="dart-async/Stream/cast.html">cast</a></li>
        <li><a href="dart-async/Stream/contains.html">contains</a></li>
        <li><a href="dart-async/Stream/distinct.html">distinct</a></li>
        <li><a href="dart-async/Stream/drain.html">drain</a></li>
        <li><a href="dart-async/Stream/elementAt.html">elementAt</a></li>
        <li><a href="dart-async/Stream/every.html">every</a></li>
        <li><a href="dart-async/Stream/expand.html">expand</a></li>
        <li><a href="dart-async/Stream/firstWhere.html">firstWhere</a></li>
        <li><a href="dart-async/Stream/fold.html">fold</a></li>
        <li><a href="dart-async/Stream/forEach.html">forEach</a></li>
        <li><a href="dart-async/Stream/handleError.html">handleError</a></li>
        <li><a href="dart-async/Stream/join.html">join</a></li>
        <li><a href="dart-async/Stream/lastWhere.html">lastWhere</a></li>
        <li><a href="dart-async/Stream/listen.html">listen</a></li>
        <li><a href="dart-async/Stream/map.html">map</a></li>
        <li><a href="dart-async/Stream/pipe.html">pipe</a></li>
        <li><a href="dart-async/Stream/reduce.html">reduce</a></li>
        <li><a href="dart-async/Stream/singleWhere.html">singleWhere</a></li>
        <li><a href="dart-async/Stream/skip.html">skip</a></li>
        <li><a href="dart-async/Stream/skipWhile.html">skipWhile</a></li>
        <li><a href="dart-async/Stream/take.html">take</a></li>
        <li><a href="dart-async/Stream/takeWhile.html">takeWhile</a></li>
        <li><a href="dart-async/Stream/timeout.html">timeout</a></li>
        <li><a href="dart-async/Stream/toList.html">toList</a></li>
        <li><a href="dart-async/Stream/toSet.html">toSet</a></li>
        <li><a href="dart-async/Stream/transform.html">transform</a></li>
        <li><a href="dart-async/Stream/where.html">where</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-async/Stream-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
        <li class="section-title"><a href="dart-async/Stream-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-async/Stream/castFrom.html">castFrom</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">join</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-async/Future-class.html">Future</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>&gt;</span></span>
            <span class="name ">join</span>
(<wbr>[<span class="parameter" id="join-param-separator"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">separator</span> = <span class="default-value">""</span></span> ])
      
    </section>
    <section class="desc markdown">
      <p>Combines the string representation of elements into a single string.</p>
<p>Each element is converted to a string using its <a href="dart-core/Object/toString.html">Object.toString</a> method.
If <code>separator</code> is provided, it is inserted between element string
representations.</p>
<p>The returned future is completed with the combined string when this stream
is done.</p>
<p>If this stream emits an error, or the call to <a href="dart-core/Object/toString.html">Object.toString</a> throws,
the returned future is completed with that error,
and processing stops.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Future&lt;String&gt; join([String separator = &quot;&quot;]) {
  _Future&lt;String&gt; result = new _Future&lt;String&gt;();
  StringBuffer buffer = new StringBuffer();
  bool first = true;
  StreamSubscription&lt;T&gt; subscription =
      this.listen(null, onError: result._completeError, onDone: () {
    result._complete(buffer.toString());
  }, cancelOnError: true);
  subscription.onData(separator.isEmpty
      ? (T element) {
          try {
            buffer.write(element);
          } catch (e, s) {
            _cancelAndErrorWithReplacement(subscription, result, e, s);
          }
        }
      : (T element) {
          if (!first) {
            buffer.write(separator);
          }
          first = false;
          try {
            buffer.write(element);
          } catch (e, s) {
            _cancelAndErrorWithReplacement(subscription, result, e, s);
          }
        });
  return result;
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
