<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the handleError method from the Stream class, for the Dart programming language.">
  <title>handleError method - Stream class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li><a href="dart-async/Stream-class.html">Stream<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></a></li>
    <li class="self-crumb">handleError method</li>
  </ol>
  <div class="self-name">handleError</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li><a href="dart-async/Stream-class.html">Stream<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></a></li>
      <li class="self-crumb">handleError method</li>
    </ol>
    
    <h5>Stream class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-async/Stream-class.html#constructors">Constructors</a></li>
        <li><a href="dart-async/Stream/Stream.html">Stream</a></li>
        <li><a href="dart-async/Stream/Stream.empty.html">empty</a></li>
        <li><a href="dart-async/Stream/Stream.error.html">error</a></li>
        <li><a href="dart-async/Stream/Stream.eventTransformed.html">eventTransformed</a></li>
        <li><a href="dart-async/Stream/Stream.fromFuture.html">fromFuture</a></li>
        <li><a href="dart-async/Stream/Stream.fromFutures.html">fromFutures</a></li>
        <li><a href="dart-async/Stream/Stream.fromIterable.html">fromIterable</a></li>
        <li><a href="dart-async/Stream/Stream.multi.html">multi</a></li>
        <li><a href="dart-async/Stream/Stream.periodic.html">periodic</a></li>
        <li><a href="dart-async/Stream/Stream.value.html">value</a></li>
    
        <li class="section-title">
            <a href="dart-async/Stream-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-async/Stream/first.html">first</a></li>
        <li><a href="dart-async/Stream/isBroadcast.html">isBroadcast</a></li>
        <li><a href="dart-async/Stream/isEmpty.html">isEmpty</a></li>
        <li><a href="dart-async/Stream/last.html">last</a></li>
        <li><a href="dart-async/Stream/length.html">length</a></li>
        <li><a href="dart-async/Stream/single.html">single</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-async/Stream-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-async/Stream/any.html">any</a></li>
        <li><a href="dart-async/Stream/asBroadcastStream.html">asBroadcastStream</a></li>
        <li><a href="dart-async/Stream/asyncExpand.html">asyncExpand</a></li>
        <li><a href="dart-async/Stream/asyncMap.html">asyncMap</a></li>
        <li><a href="dart-async/Stream/cast.html">cast</a></li>
        <li><a href="dart-async/Stream/contains.html">contains</a></li>
        <li><a href="dart-async/Stream/distinct.html">distinct</a></li>
        <li><a href="dart-async/Stream/drain.html">drain</a></li>
        <li><a href="dart-async/Stream/elementAt.html">elementAt</a></li>
        <li><a href="dart-async/Stream/every.html">every</a></li>
        <li><a href="dart-async/Stream/expand.html">expand</a></li>
        <li><a href="dart-async/Stream/firstWhere.html">firstWhere</a></li>
        <li><a href="dart-async/Stream/fold.html">fold</a></li>
        <li><a href="dart-async/Stream/forEach.html">forEach</a></li>
        <li><a href="dart-async/Stream/handleError.html">handleError</a></li>
        <li><a href="dart-async/Stream/join.html">join</a></li>
        <li><a href="dart-async/Stream/lastWhere.html">lastWhere</a></li>
        <li><a href="dart-async/Stream/listen.html">listen</a></li>
        <li><a href="dart-async/Stream/map.html">map</a></li>
        <li><a href="dart-async/Stream/pipe.html">pipe</a></li>
        <li><a href="dart-async/Stream/reduce.html">reduce</a></li>
        <li><a href="dart-async/Stream/singleWhere.html">singleWhere</a></li>
        <li><a href="dart-async/Stream/skip.html">skip</a></li>
        <li><a href="dart-async/Stream/skipWhile.html">skipWhile</a></li>
        <li><a href="dart-async/Stream/take.html">take</a></li>
        <li><a href="dart-async/Stream/takeWhile.html">takeWhile</a></li>
        <li><a href="dart-async/Stream/timeout.html">timeout</a></li>
        <li><a href="dart-async/Stream/toList.html">toList</a></li>
        <li><a href="dart-async/Stream/toSet.html">toSet</a></li>
        <li><a href="dart-async/Stream/transform.html">transform</a></li>
        <li><a href="dart-async/Stream/where.html">where</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-async/Stream-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
        <li class="section-title"><a href="dart-async/Stream-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-async/Stream/castFrom.html">castFrom</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">handleError</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-async/Stream-class.html">Stream</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span>
            <span class="name ">handleError</span>
(<wbr><span class="parameter" id="handleError-param-onError"><span class="type-annotation"><a href="dart-core/Function-class.html">Function</a></span> <span class="parameter-name">onError</span>, {</span> <span class="parameter" id="handleError-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-error"><span class="type-annotation">dynamic</span> <span class="parameter-name">error</span></span>)</span> })
      
    </section>
    <section class="desc markdown">
      <p>Creates a wrapper Stream that intercepts some errors from this stream.</p>
<p>If this stream sends an error that matches <code>test</code>, then it is intercepted
by the <code>onError</code> function.</p>
<p>The <code>onError</code> callback must be of type <code>void Function(Object error)</code> or
<code>void Function(Object error, StackTrace)</code>.
The function type determines whether <code>onError</code> is invoked with a stack
trace argument.
The stack trace argument may be <a href="dart-core/StackTrace/empty-constant.html">StackTrace.empty</a> if this stream received
an error without a stack trace.</p>
<p>An asynchronous error <code>error</code> is matched by a test function if
<code>test(error)</code> returns true. If <code>test</code> is omitted, every error is considered
matching.</p>
<p>If the error is intercepted, the <code>onError</code> function can decide what to do
with it. It can throw if it wants to raise a new (or the same) error,
or simply return to make this stream forget the error.
If the received <code>error</code> value is thrown again by the <code>onError</code> function,
it acts like a <code>rethrow</code> and it is emitted along with its original
stack trace, not the stack trace of the <code>throw</code> inside <code>onError</code>.</p>
<p>If you need to transform an error into a data event, use the more generic
<a href="dart-async/Stream/transform.html">Stream.transform</a> to handle the event by writing a data event to
the output sink.</p>
<p>The returned stream is a broadcast stream if this stream is.
If a broadcast stream is listened to more than once, each subscription
will individually perform the <code>test</code> and handle the error.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Stream&lt;T&gt; handleError(Function onError, {bool test(error)?}) {
  if (onError is! void Function(Object, StackTrace) &amp;&amp;
      onError is! void Function(Object)) {
    throw ArgumentError.value(
        onError,
        &quot;onError&quot;,
        &quot;Error handler must accept one Object or one Object and a StackTrace&quot;
            &quot; as arguments.&quot;);
  }
  return new _HandleErrorStream&lt;T&gt;(this, onError, test);
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
