<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the RunHandler property from the dart:async library, for the Dart programming language.">
  <title>RunHandler typedef - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li class="self-crumb">RunHandler typedef</li>
  </ol>
  <div class="self-name">RunHandler</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li class="self-crumb">RunHandler typedef</li>
    </ol>
    
    <h5>dart:async library</h5>
    <ol>
      <li class="section-title"><a href="dart-async/dart-async-library.html#classes">Classes</a></li>
      <li><a href="dart-async/Completer-class.html">Completer</a></li>
      <li><a class="deprecated" href="dart-async/DeferredLibrary-class.html">DeferredLibrary</a></li>
      <li><a href="dart-async/EventSink-class.html">EventSink</a></li>
      <li><a href="dart-async/Future-class.html">Future</a></li>
      <li><a href="dart-async/FutureOr-class.html">FutureOr</a></li>
      <li><a href="dart-async/MultiStreamController-class.html">MultiStreamController</a></li>
      <li><a href="dart-async/Stream-class.html">Stream</a></li>
      <li><a href="dart-async/StreamConsumer-class.html">StreamConsumer</a></li>
      <li><a href="dart-async/StreamController-class.html">StreamController</a></li>
      <li><a href="dart-async/StreamIterator-class.html">StreamIterator</a></li>
      <li><a href="dart-async/StreamSink-class.html">StreamSink</a></li>
      <li><a href="dart-async/StreamSubscription-class.html">StreamSubscription</a></li>
      <li><a href="dart-async/StreamTransformer-class.html">StreamTransformer</a></li>
      <li><a href="dart-async/StreamTransformerBase-class.html">StreamTransformerBase</a></li>
      <li><a href="dart-async/StreamView-class.html">StreamView</a></li>
      <li><a href="dart-async/SynchronousStreamController-class.html">SynchronousStreamController</a></li>
      <li><a href="dart-async/Timer-class.html">Timer</a></li>
      <li><a href="dart-async/Zone-class.html">Zone</a></li>
      <li><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></li>
      <li><a href="dart-async/ZoneSpecification-class.html">ZoneSpecification</a></li>
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#extension">Extensions</a></li>
      <li><a href="dart-async/FutureExtensions.html">FutureExtensions</a></li>
    
    
    
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#functions">Functions</a></li>
      <li><a href="dart-async/runZoned.html">runZoned</a></li>
      <li><a href="dart-async/runZonedGuarded.html">runZonedGuarded</a></li>
      <li><a href="dart-async/scheduleMicrotask.html">scheduleMicrotask</a></li>
      <li><a href="dart-async/unawaited.html">unawaited</a></li>
    
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-async/ControllerCallback.html">ControllerCallback</a></li>
      <li><a href="dart-async/ControllerCancelCallback.html">ControllerCancelCallback</a></li>
      <li><a href="dart-async/CreatePeriodicTimerHandler.html">CreatePeriodicTimerHandler</a></li>
      <li><a href="dart-async/CreateTimerHandler.html">CreateTimerHandler</a></li>
      <li><a href="dart-async/ErrorCallbackHandler.html">ErrorCallbackHandler</a></li>
      <li><a href="dart-async/ForkHandler.html">ForkHandler</a></li>
      <li><a href="dart-async/HandleUncaughtErrorHandler.html">HandleUncaughtErrorHandler</a></li>
      <li><a href="dart-async/PrintHandler.html">PrintHandler</a></li>
      <li><a href="dart-async/RegisterBinaryCallbackHandler.html">RegisterBinaryCallbackHandler</a></li>
      <li><a href="dart-async/RegisterCallbackHandler.html">RegisterCallbackHandler</a></li>
      <li><a href="dart-async/RegisterUnaryCallbackHandler.html">RegisterUnaryCallbackHandler</a></li>
      <li><a href="dart-async/RunBinaryHandler.html">RunBinaryHandler</a></li>
      <li><a href="dart-async/RunHandler.html">RunHandler</a></li>
      <li><a href="dart-async/RunUnaryHandler.html">RunUnaryHandler</a></li>
      <li><a href="dart-async/ScheduleMicrotaskHandler.html">ScheduleMicrotaskHandler</a></li>
      <li><a href="dart-async/ZoneBinaryCallback.html">ZoneBinaryCallback</a></li>
      <li><a href="dart-async/ZoneCallback.html">ZoneCallback</a></li>
      <li><a href="dart-async/ZoneUnaryCallback.html">ZoneUnaryCallback</a></li>
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-async/AsyncError-class.html">AsyncError</a></li>
      <li><a href="dart-async/DeferredLoadException-class.html">DeferredLoadException</a></li>
      <li><a href="dart-async/TimeoutException-class.html">TimeoutException</a></li>
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-typedef">RunHandler</span> typedef </h1></div>

    <section class="multi-line-signature">
        <span class="returntype">R</span>
                <span class="name ">RunHandler</span>
&lt;<wbr><span class="type-parameter">R</span>&gt;(<wbr><span class="parameter" id="RunHandler-param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="RunHandler-param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="RunHandler-param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="RunHandler-param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>()</span>)
    </section>

    <section class="desc markdown">
      <p>The type of a custom <a href="dart-async/Zone/run.html">Zone.run</a> implementation function.</p>
<p>Receives the <a href="dart-async/Zone-class.html">Zone</a> that the handler was registered on as <code>self</code>,
a delegate forwarding to the handlers of <code>self</code>'s parent zone as <code>parent</code>,
and the current zone where the error was uncaught as <code>zone</code>,
which will have <code>self</code> as a parent zone.</p>
<p>The function <code>f</code> is the function which was passed to the
<a href="dart-async/Zone/run.html">Zone.run</a> of <code>zone</code>.</p>
<p>The default behavior of <a href="dart-async/Zone/run.html">Zone.run</a> is
to call <code>f</code> in the current zone, <code>zone</code>.
A custom handler can do things before, after or instead of
calling <code>f</code>.</p>
<p>The function must only access zone-related functionality through
<code>self</code>, <code>parent</code> or <code>zone</code>.
It should not depend on the current zone (<a href="dart-async/Zone/current.html">Zone.current</a>).</p>
    </section>
        <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">typedef RunHandler = R Function&lt;R&gt;(
    Zone self, ZoneDelegate parent, Zone zone, R Function() f);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
