<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the ZoneSpecification class from the dart:async library, for the Dart programming language.">
  <title>ZoneSpecification class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li class="self-crumb">ZoneSpecification abstract class</li>
  </ol>
  <div class="self-name">ZoneSpecification</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li class="self-crumb">ZoneSpecification abstract class</li>
    </ol>
    
    <h5>dart:async library</h5>
    <ol>
      <li class="section-title"><a href="dart-async/dart-async-library.html#classes">Classes</a></li>
      <li><a href="dart-async/Completer-class.html">Completer</a></li>
      <li><a class="deprecated" href="dart-async/DeferredLibrary-class.html">DeferredLibrary</a></li>
      <li><a href="dart-async/EventSink-class.html">EventSink</a></li>
      <li><a href="dart-async/Future-class.html">Future</a></li>
      <li><a href="dart-async/FutureOr-class.html">FutureOr</a></li>
      <li><a href="dart-async/MultiStreamController-class.html">MultiStreamController</a></li>
      <li><a href="dart-async/Stream-class.html">Stream</a></li>
      <li><a href="dart-async/StreamConsumer-class.html">StreamConsumer</a></li>
      <li><a href="dart-async/StreamController-class.html">StreamController</a></li>
      <li><a href="dart-async/StreamIterator-class.html">StreamIterator</a></li>
      <li><a href="dart-async/StreamSink-class.html">StreamSink</a></li>
      <li><a href="dart-async/StreamSubscription-class.html">StreamSubscription</a></li>
      <li><a href="dart-async/StreamTransformer-class.html">StreamTransformer</a></li>
      <li><a href="dart-async/StreamTransformerBase-class.html">StreamTransformerBase</a></li>
      <li><a href="dart-async/StreamView-class.html">StreamView</a></li>
      <li><a href="dart-async/SynchronousStreamController-class.html">SynchronousStreamController</a></li>
      <li><a href="dart-async/Timer-class.html">Timer</a></li>
      <li><a href="dart-async/Zone-class.html">Zone</a></li>
      <li><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></li>
      <li><a href="dart-async/ZoneSpecification-class.html">ZoneSpecification</a></li>
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#extension">Extensions</a></li>
      <li><a href="dart-async/FutureExtensions.html">FutureExtensions</a></li>
    
    
    
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#functions">Functions</a></li>
      <li><a href="dart-async/runZoned.html">runZoned</a></li>
      <li><a href="dart-async/runZonedGuarded.html">runZonedGuarded</a></li>
      <li><a href="dart-async/scheduleMicrotask.html">scheduleMicrotask</a></li>
      <li><a href="dart-async/unawaited.html">unawaited</a></li>
    
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-async/ControllerCallback.html">ControllerCallback</a></li>
      <li><a href="dart-async/ControllerCancelCallback.html">ControllerCancelCallback</a></li>
      <li><a href="dart-async/CreatePeriodicTimerHandler.html">CreatePeriodicTimerHandler</a></li>
      <li><a href="dart-async/CreateTimerHandler.html">CreateTimerHandler</a></li>
      <li><a href="dart-async/ErrorCallbackHandler.html">ErrorCallbackHandler</a></li>
      <li><a href="dart-async/ForkHandler.html">ForkHandler</a></li>
      <li><a href="dart-async/HandleUncaughtErrorHandler.html">HandleUncaughtErrorHandler</a></li>
      <li><a href="dart-async/PrintHandler.html">PrintHandler</a></li>
      <li><a href="dart-async/RegisterBinaryCallbackHandler.html">RegisterBinaryCallbackHandler</a></li>
      <li><a href="dart-async/RegisterCallbackHandler.html">RegisterCallbackHandler</a></li>
      <li><a href="dart-async/RegisterUnaryCallbackHandler.html">RegisterUnaryCallbackHandler</a></li>
      <li><a href="dart-async/RunBinaryHandler.html">RunBinaryHandler</a></li>
      <li><a href="dart-async/RunHandler.html">RunHandler</a></li>
      <li><a href="dart-async/RunUnaryHandler.html">RunUnaryHandler</a></li>
      <li><a href="dart-async/ScheduleMicrotaskHandler.html">ScheduleMicrotaskHandler</a></li>
      <li><a href="dart-async/ZoneBinaryCallback.html">ZoneBinaryCallback</a></li>
      <li><a href="dart-async/ZoneCallback.html">ZoneCallback</a></li>
      <li><a href="dart-async/ZoneUnaryCallback.html">ZoneUnaryCallback</a></li>
    
      <li class="section-title"><a href="dart-async/dart-async-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-async/AsyncError-class.html">AsyncError</a></li>
      <li><a href="dart-async/DeferredLoadException-class.html">DeferredLoadException</a></li>
      <li><a href="dart-async/TimeoutException-class.html">TimeoutException</a></li>
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">ZoneSpecification</span> class </h1></div>

    <section class="desc markdown">
      <p>A parameter object with custom zone function handlers for <a href="dart-async/Zone/fork.html">Zone.fork</a>.</p>
<p>A zone specification is a parameter object passed to <a href="dart-async/Zone/fork.html">Zone.fork</a>
and any underlying <a href="dart-async/ForkHandler.html">ForkHandler</a> custom implementations.
The individual handlers, if set to a non-null value,
will be the implementation of the corresponding <a href="dart-async/Zone-class.html">Zone</a> methods
for a forked zone created using this zone specification.</p>
<p>Handlers have the same signature as the same-named methods on <a href="dart-async/Zone-class.html">Zone</a>,
but receive three additional arguments:</p>
<ol>
<li>The zone the handlers are attached to (the "self" zone).
This is the zone created by <a href="dart-async/Zone/fork.html">Zone.fork</a> where the handler is
passed as part of the zone delegation.</li>
<li>A <a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a> to the parent zone.</li>
<li>The "current" zone at the time the request was made.
The self zone is always a parent zone of the current zone.</li>
</ol>
<p>Handlers can either stop propagating the request (by simply not calling the
parent handler), or forward to the parent zone, potentially modifying the
arguments on the way.</p>
    </section>
    

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="ZoneSpecification" class="callable">
          <span class="name"><a href="dart-async/ZoneSpecification/ZoneSpecification.html">ZoneSpecification</a></span><span class="signature">({<span class="parameter" id="-param-handleUncaughtError"><span class="type-annotation"><a href="dart-async/HandleUncaughtErrorHandler.html">HandleUncaughtErrorHandler</a></span> <span class="parameter-name">handleUncaughtError</span>, </span> <span class="parameter" id="-param-run"><span class="type-annotation">R</span> <span class="parameter-name">run</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>()</span>), </span> <span class="parameter" id="-param-runUnary"><span class="type-annotation">R</span> <span class="parameter-name">runUnary</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>), </span> <span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>), </span> <span class="parameter" id="-param-runBinary"><span class="type-annotation">R</span> <span class="parameter-name">runBinary</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>), </span> <span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>), </span> <span class="parameter" id="-param-registerCallback"><span class="type-annotation"><a href="dart-async/ZoneCallback.html">ZoneCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>&gt;</span></span> <span class="parameter-name">registerCallback</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>()</span>), </span> <span class="parameter" id="-param-registerUnaryCallback"><span class="type-annotation"><a href="dart-async/ZoneUnaryCallback.html">ZoneUnaryCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">registerUnaryCallback</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>)</span>), </span> <span class="parameter" id="-param-registerBinaryCallback"><span class="type-annotation"><a href="dart-async/ZoneBinaryCallback.html">ZoneBinaryCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T1</span>, <span class="type-parameter">T2</span>&gt;</span></span> <span class="parameter-name">registerBinaryCallback</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>)</span>), </span> <span class="parameter" id="-param-errorCallback"><span class="type-annotation"><a href="dart-async/ErrorCallbackHandler.html">ErrorCallbackHandler</a></span> <span class="parameter-name">errorCallback</span>, </span> <span class="parameter" id="-param-scheduleMicrotask"><span class="type-annotation"><a href="dart-async/ScheduleMicrotaskHandler.html">ScheduleMicrotaskHandler</a></span> <span class="parameter-name">scheduleMicrotask</span>, </span> <span class="parameter" id="-param-createTimer"><span class="type-annotation"><a href="dart-async/CreateTimerHandler.html">CreateTimerHandler</a></span> <span class="parameter-name">createTimer</span>, </span> <span class="parameter" id="-param-createPeriodicTimer"><span class="type-annotation"><a href="dart-async/CreatePeriodicTimerHandler.html">CreatePeriodicTimerHandler</a></span> <span class="parameter-name">createPeriodicTimer</span>, </span> <span class="parameter" id="-param-print"><span class="type-annotation"><a href="dart-async/PrintHandler.html">PrintHandler</a></span> <span class="parameter-name">print</span>, </span> <span class="parameter" id="-param-fork"><span class="type-annotation"><a href="dart-async/ForkHandler.html">ForkHandler</a></span> <span class="parameter-name">fork</span></span> })</span>
        </dt>
        <dd>
          Creates a specification with the provided handlers. <a href="dart-async/ZoneSpecification/ZoneSpecification.html">[...]</a>
          <div class="constructor-modifier features">const</div>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="ZoneSpecification.from" class="callable">
          <span class="name"><a href="dart-async/ZoneSpecification/ZoneSpecification.from.html">ZoneSpecification.from</a></span><span class="signature">(<span class="parameter" id="from-param-other"><span class="type-annotation"><a href="dart-async/ZoneSpecification-class.html">ZoneSpecification</a></span> <span class="parameter-name">other</span>, {</span> <span class="parameter" id="from-param-handleUncaughtError"><span class="type-annotation"><a href="dart-async/HandleUncaughtErrorHandler.html">HandleUncaughtErrorHandler</a></span> <span class="parameter-name">handleUncaughtError</span>, </span> <span class="parameter" id="from-param-run"><span class="type-annotation">R</span> <span class="parameter-name">run</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>()</span>), </span> <span class="parameter" id="from-param-runUnary"><span class="type-annotation">R</span> <span class="parameter-name">runUnary</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>), </span> <span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>), </span> <span class="parameter" id="from-param-runBinary"><span class="type-annotation">R</span> <span class="parameter-name">runBinary</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>), </span> <span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>), </span> <span class="parameter" id="from-param-registerCallback"><span class="type-annotation"><a href="dart-async/ZoneCallback.html">ZoneCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>&gt;</span></span> <span class="parameter-name">registerCallback</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>()</span>), </span> <span class="parameter" id="from-param-registerUnaryCallback"><span class="type-annotation"><a href="dart-async/ZoneUnaryCallback.html">ZoneUnaryCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">registerUnaryCallback</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>)</span>), </span> <span class="parameter" id="from-param-registerBinaryCallback"><span class="type-annotation"><a href="dart-async/ZoneBinaryCallback.html">ZoneBinaryCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T1</span>, <span class="type-parameter">T2</span>&gt;</span></span> <span class="parameter-name">registerBinaryCallback</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>)</span>), </span> <span class="parameter" id="from-param-errorCallback"><span class="type-annotation"><a href="dart-async/ErrorCallbackHandler.html">ErrorCallbackHandler</a></span> <span class="parameter-name">errorCallback</span>, </span> <span class="parameter" id="from-param-scheduleMicrotask"><span class="type-annotation"><a href="dart-async/ScheduleMicrotaskHandler.html">ScheduleMicrotaskHandler</a></span> <span class="parameter-name">scheduleMicrotask</span>, </span> <span class="parameter" id="from-param-createTimer"><span class="type-annotation"><a href="dart-async/CreateTimerHandler.html">CreateTimerHandler</a></span> <span class="parameter-name">createTimer</span>, </span> <span class="parameter" id="from-param-createPeriodicTimer"><span class="type-annotation"><a href="dart-async/CreatePeriodicTimerHandler.html">CreatePeriodicTimerHandler</a></span> <span class="parameter-name">createPeriodicTimer</span>, </span> <span class="parameter" id="from-param-print"><span class="type-annotation"><a href="dart-async/PrintHandler.html">PrintHandler</a></span> <span class="parameter-name">print</span>, </span> <span class="parameter" id="from-param-fork"><span class="type-annotation"><a href="dart-async/ForkHandler.html">ForkHandler</a></span> <span class="parameter-name">fork</span></span> })</span>
        </dt>
        <dd>
          Creates a specification from <code>other</code> and provided handlers. <a href="dart-async/ZoneSpecification/ZoneSpecification.from.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="createPeriodicTimer" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/createPeriodicTimer.html">createPeriodicTimer</a></span>
          <span class="signature">&#8594; <a href="dart-async/CreatePeriodicTimerHandler.html">CreatePeriodicTimerHandler</a></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/createPeriodicTimer.html">Zone.createPeriodicTimer</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="createTimer" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/createTimer.html">createTimer</a></span>
          <span class="signature">&#8594; <a href="dart-async/CreateTimerHandler.html">CreateTimerHandler</a></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/createTimer.html">Zone.createTimer</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="errorCallback" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/errorCallback.html">errorCallback</a></span>
          <span class="signature">&#8594; <a href="dart-async/ErrorCallbackHandler.html">ErrorCallbackHandler</a></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/errorCallback.html">Zone.errorCallback</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="fork" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/fork.html">fork</a></span>
          <span class="signature">&#8594; <a href="dart-async/ForkHandler.html">ForkHandler</a></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/handleUncaughtError.html">Zone.handleUncaughtError</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="handleUncaughtError" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/handleUncaughtError.html">handleUncaughtError</a></span>
          <span class="signature">&#8594; <a href="dart-async/HandleUncaughtErrorHandler.html">HandleUncaughtErrorHandler</a></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/handleUncaughtError.html">Zone.handleUncaughtError</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="print" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/print.html">print</a></span>
          <span class="signature">&#8594; <a href="dart-async/PrintHandler.html">PrintHandler</a></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/print.html">Zone.print</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="registerBinaryCallback" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/registerBinaryCallback.html">registerBinaryCallback</a></span>
          <span class="signature">&#8594; <a href="dart-async/ZoneBinaryCallback.html">ZoneBinaryCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T1</span>, <span class="type-parameter">T2</span>&gt;</span> Function&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T1</span>, <span class="type-parameter">T2</span>&gt;<span class="signature">(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>)</span>)</span></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/registerBinaryCallback.html">Zone.registerBinaryCallback</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="registerCallback" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/registerCallback.html">registerCallback</a></span>
          <span class="signature">&#8594; <a href="dart-async/ZoneCallback.html">ZoneCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>&gt;</span> Function&lt;<wbr><span class="type-parameter">R</span>&gt;<span class="signature">(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>()</span>)</span></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/registerCallback.html">Zone.registerCallback</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="registerUnaryCallback" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/registerUnaryCallback.html">registerUnaryCallback</a></span>
          <span class="signature">&#8594; <a href="dart-async/ZoneUnaryCallback.html">ZoneUnaryCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T</span>&gt;</span> Function&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T</span>&gt;<span class="signature">(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>)</span>)</span></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/registerUnaryCallback.html">Zone.registerUnaryCallback</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="run" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/run.html">run</a></span>
          <span class="signature">&#8594; R Function&lt;<wbr><span class="type-parameter">R</span>&gt;<span class="signature">(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>()</span>)</span></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/run.html">Zone.run</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="runBinary" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/runBinary.html">runBinary</a></span>
          <span class="signature">&#8594; R Function&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T1</span>, <span class="type-parameter">T2</span>&gt;<span class="signature">(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>), </span> <span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>)</span></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/runBinary.html">Zone.runBinary</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="runUnary" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/runUnary.html">runUnary</a></span>
          <span class="signature">&#8594; R Function&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T</span>&gt;<span class="signature">(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>), </span> <span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>)</span></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/runUnary.html">Zone.runUnary</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="scheduleMicrotask" class="property">
          <span class="name"><a href="dart-async/ZoneSpecification/scheduleMicrotask.html">scheduleMicrotask</a></span>
          <span class="signature">&#8594; <a href="dart-async/ScheduleMicrotaskHandler.html">ScheduleMicrotaskHandler</a></span>         
        </dt>
        <dd>
          A custom <a href="dart-async/Zone/scheduleMicrotask.html">Zone.scheduleMicrotask</a> implementation for a new zone.
                  <div class="features">read-only</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-core/Object/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-core/Object/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-async/ZoneSpecification-class.html#constructors">Constructors</a></li>
      <li><a href="dart-async/ZoneSpecification/ZoneSpecification.html">ZoneSpecification</a></li>
      <li><a href="dart-async/ZoneSpecification/ZoneSpecification.from.html">from</a></li>
    
      <li class="section-title">
        <a href="dart-async/ZoneSpecification-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-async/ZoneSpecification/createPeriodicTimer.html">createPeriodicTimer</a></li>
      <li><a href="dart-async/ZoneSpecification/createTimer.html">createTimer</a></li>
      <li><a href="dart-async/ZoneSpecification/errorCallback.html">errorCallback</a></li>
      <li><a href="dart-async/ZoneSpecification/fork.html">fork</a></li>
      <li><a href="dart-async/ZoneSpecification/handleUncaughtError.html">handleUncaughtError</a></li>
      <li><a href="dart-async/ZoneSpecification/print.html">print</a></li>
      <li><a href="dart-async/ZoneSpecification/registerBinaryCallback.html">registerBinaryCallback</a></li>
      <li><a href="dart-async/ZoneSpecification/registerCallback.html">registerCallback</a></li>
      <li><a href="dart-async/ZoneSpecification/registerUnaryCallback.html">registerUnaryCallback</a></li>
      <li><a href="dart-async/ZoneSpecification/run.html">run</a></li>
      <li><a href="dart-async/ZoneSpecification/runBinary.html">runBinary</a></li>
      <li><a href="dart-async/ZoneSpecification/runUnary.html">runUnary</a></li>
      <li><a href="dart-async/ZoneSpecification/scheduleMicrotask.html">scheduleMicrotask</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title inherited"><a href="dart-async/ZoneSpecification-class.html#instance-methods">Methods</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-async/ZoneSpecification-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
