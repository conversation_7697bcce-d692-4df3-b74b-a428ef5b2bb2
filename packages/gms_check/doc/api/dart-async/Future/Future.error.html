<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the Future.error constructor from the Class Future class from the dart:async library, for the Dart programming language.">
  <title>Future.error constructor - Future class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li><a href="dart-async/Future-class.html">Future<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></a></li>
    <li class="self-crumb">Future.error factory constructor</li>
  </ol>
  <div class="self-name">Future.error</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li><a href="dart-async/Future-class.html">Future<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></a></li>
      <li class="self-crumb">Future.error factory constructor</li>
    </ol>
    
    <h5>Future class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-async/Future-class.html#constructors">Constructors</a></li>
      <li><a href="dart-async/Future/Future.html">Future</a></li>
      <li><a href="dart-async/Future/Future.delayed.html">delayed</a></li>
      <li><a href="dart-async/Future/Future.error.html">error</a></li>
      <li><a href="dart-async/Future/Future.microtask.html">microtask</a></li>
      <li><a href="dart-async/Future/Future.sync.html">sync</a></li>
      <li><a href="dart-async/Future/Future.value.html">value</a></li>
    
      <li class="section-title inherited">
        <a href="dart-async/Future-class.html#instance-properties">Properties</a>
      </li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-async/Future-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-async/Future/asStream.html">asStream</a></li>
      <li><a href="dart-async/Future/catchError.html">catchError</a></li>
      <li><a href="dart-async/Future/then.html">then</a></li>
      <li><a href="dart-async/Future/timeout.html">timeout</a></li>
      <li><a href="dart-async/Future/whenComplete.html">whenComplete</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-async/Future-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
      <li class="section-title"><a href="dart-async/Future-class.html#static-methods">Static methods</a></li>
      <li><a href="dart-async/Future/any.html">any</a></li>
      <li><a href="dart-async/Future/doWhile.html">doWhile</a></li>
      <li><a href="dart-async/Future/forEach.html">forEach</a></li>
      <li><a href="dart-async/Future/wait.html">wait</a></li>
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">Future&lt;<wbr><span class="type-parameter">T</span>&gt;.error</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">Future&lt;<wbr><span class="type-parameter">T</span>&gt;.error</span>(<wbr><span class="parameter" id="error-param-error"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">error</span>, [</span> <span class="parameter" id="error-param-stackTrace"><span class="type-annotation"><a href="dart-core/StackTrace-class.html">StackTrace</a></span> <span class="parameter-name">stackTrace</span></span> ])
    </section>

    <section class="desc markdown">
      <p>Creates a future that completes with an error.</p>
<p>The created future will be completed with an error in a future microtask.
This allows enough time for someone to add an error handler on the future.
If an error handler isn't added before the future completes, the error
will be considered unhandled.</p>
<p>Use <a href="dart-async/Completer-class.html">Completer</a> to create a future and complete it later.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">factory Future.error(Object error, [StackTrace? stackTrace]) {
  &#47;&#47; TODO(40614): Remove once non-nullability is sound.
  checkNotNullable(error, &quot;error&quot;);
  if (!identical(Zone.current, _rootZone)) {
    AsyncError? replacement = Zone.current.errorCallback(error, stackTrace);
    if (replacement != null) {
      error = replacement.error;
      stackTrace = replacement.stackTrace;
    }
  }
  stackTrace ??= AsyncError.defaultStackTrace(error);
  return new _Future&lt;T&gt;.immediateError(error, stackTrace);
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
