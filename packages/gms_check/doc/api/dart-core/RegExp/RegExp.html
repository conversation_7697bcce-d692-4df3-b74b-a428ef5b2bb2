<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the RegExp constructor from the Class RegExp class from the dart:core library, for the Dart programming language.">
  <title>RegExp constructor - RegExp class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/RegExp-class.html">RegExp</a></li>
    <li class="self-crumb">RegExp factory constructor</li>
  </ol>
  <div class="self-name">RegExp</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/RegExp-class.html">RegExp</a></li>
      <li class="self-crumb">RegExp factory constructor</li>
    </ol>
    
    <h5>RegExp class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-core/RegExp-class.html#constructors">Constructors</a></li>
      <li><a href="dart-core/RegExp/RegExp.html">RegExp</a></li>
    
      <li class="section-title">
        <a href="dart-core/RegExp-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-core/RegExp/isCaseSensitive.html">isCaseSensitive</a></li>
      <li><a href="dart-core/RegExp/isDotAll.html">isDotAll</a></li>
      <li><a href="dart-core/RegExp/isMultiLine.html">isMultiLine</a></li>
      <li><a href="dart-core/RegExp/isUnicode.html">isUnicode</a></li>
      <li><a href="dart-core/RegExp/pattern.html">pattern</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-core/RegExp-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-core/RegExp/allMatches.html">allMatches</a></li>
      <li><a href="dart-core/RegExp/firstMatch.html">firstMatch</a></li>
      <li><a href="dart-core/RegExp/hasMatch.html">hasMatch</a></li>
      <li><a href="dart-core/RegExp/stringMatch.html">stringMatch</a></li>
      <li class="inherited"><a href="dart-core/Pattern/matchAsPrefix.html">matchAsPrefix</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-core/RegExp-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
      <li class="section-title"><a href="dart-core/RegExp-class.html#static-methods">Static methods</a></li>
      <li><a href="dart-core/RegExp/escape.html">escape</a></li>
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">RegExp</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">RegExp</span>(<wbr><span class="parameter" id="-param-source"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">source</span>, {</span> <span class="parameter" id="-param-multiLine"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">multiLine</span>: <span class="default-value">false</span></span> <span class="parameter" id="-param-caseSensitive"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">caseSensitive</span>: <span class="default-value">true</span></span> <span class="parameter" id="-param-unicode"><span>@Since(&quot;2.4&quot;)</span> <span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">unicode</span>: <span class="default-value">false</span></span> <span class="parameter" id="-param-dotAll"><span>@Since(&quot;2.4&quot;)</span> <span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">dotAll</span>: <span class="default-value">false</span></span> })
    </section>

    <section class="desc markdown">
      <p>Constructs a regular expression.</p>
<p>Throws a <a href="dart-core/FormatException-class.html">FormatException</a> if <code>source</code> is not valid regular
expression syntax.</p>
<p>If <code>multiLine</code> is enabled, then <code>^</code> and <code>$</code> will match the beginning and
end of a <em>line</em>, in addition to matching beginning and end of input,
respectively.</p>
<p>If <code>caseSensitive</code> is disabled, then case is ignored.</p>
<p>If <code>unicode</code> is enabled, then the pattern is treated as a Unicode
pattern as described by the ECMAScript standard.</p>
<p>If <code>dotAll</code> is enabled, then the <code>.</code> pattern will match <em>all</em> characters,
including line terminators.</p>
<p>Example:</p>
<pre class="language-dart"><code class="language-dart">var wordPattern = RegExp(r"(\w+)");
var bracketedNumberValue = RegExp("$key: \\[\\d+\\]");
</code></pre>
<p>Notice the use of a <em>raw string</em> in the first example, and a regular
string in the second. Because of the many escapes, like <code>\d</code>, used in
regular expressions, it is common to use a raw string here, unless string
interpolation is required.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">external factory RegExp(String source,
    {bool multiLine = false,
    bool caseSensitive = true,
    @Since(&quot;2.4&quot;) bool unicode = false,
    @Since(&quot;2.4&quot;) bool dotAll = false});</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
