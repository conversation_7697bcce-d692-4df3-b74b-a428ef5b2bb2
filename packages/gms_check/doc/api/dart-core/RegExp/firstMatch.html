<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the firstMatch method from the RegExp class, for the Dart programming language.">
  <title>firstMatch method - RegExp class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/RegExp-class.html">RegExp</a></li>
    <li class="self-crumb">firstMatch abstract method</li>
  </ol>
  <div class="self-name">firstMatch</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/RegExp-class.html">RegExp</a></li>
      <li class="self-crumb">firstMatch abstract method</li>
    </ol>
    
    <h5>RegExp class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/RegExp-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/RegExp/RegExp.html">RegExp</a></li>
    
        <li class="section-title">
            <a href="dart-core/RegExp-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/RegExp/isCaseSensitive.html">isCaseSensitive</a></li>
        <li><a href="dart-core/RegExp/isDotAll.html">isDotAll</a></li>
        <li><a href="dart-core/RegExp/isMultiLine.html">isMultiLine</a></li>
        <li><a href="dart-core/RegExp/isUnicode.html">isUnicode</a></li>
        <li><a href="dart-core/RegExp/pattern.html">pattern</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/RegExp-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/RegExp/allMatches.html">allMatches</a></li>
        <li><a href="dart-core/RegExp/firstMatch.html">firstMatch</a></li>
        <li><a href="dart-core/RegExp/hasMatch.html">hasMatch</a></li>
        <li><a href="dart-core/RegExp/stringMatch.html">stringMatch</a></li>
        <li class="inherited"><a href="dart-core/Pattern/matchAsPrefix.html">matchAsPrefix</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-core/RegExp-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
        <li class="section-title"><a href="dart-core/RegExp-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-core/RegExp/escape.html">escape</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">firstMatch</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/RegExpMatch-class.html">RegExpMatch</a></span>
            <span class="name ">firstMatch</span>
(<wbr><span class="parameter" id="firstMatch-param-input"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">input</span></span>)
      
    </section>
    <section class="desc markdown">
      <p>Finds the first match of the regular expression in the string <code>input</code>.</p>
<p>Returns <code>null</code> if there is no match.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">RegExpMatch? firstMatch(String input);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
