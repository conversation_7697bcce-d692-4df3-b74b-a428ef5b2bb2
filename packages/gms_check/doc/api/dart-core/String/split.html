<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the split method from the String class, for the Dart programming language.">
  <title>split method - String class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/String-class.html">String</a></li>
    <li class="self-crumb">split abstract method</li>
  </ol>
  <div class="self-name">split</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/String-class.html">String</a></li>
      <li class="self-crumb">split abstract method</li>
    </ol>
    
    <h5>String class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/String-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/String/String.fromCharCode.html">fromCharCode</a></li>
        <li><a href="dart-core/String/String.fromCharCodes.html">fromCharCodes</a></li>
        <li><a href="dart-core/String/String.fromEnvironment.html">fromEnvironment</a></li>
    
        <li class="section-title">
            <a href="dart-core/String-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/String/codeUnits.html">codeUnits</a></li>
        <li><a href="dart-core/String/hashCode.html">hashCode</a></li>
        <li><a href="dart-core/String/isEmpty.html">isEmpty</a></li>
        <li><a href="dart-core/String/isNotEmpty.html">isNotEmpty</a></li>
        <li><a href="dart-core/String/length.html">length</a></li>
        <li><a href="dart-core/String/runes.html">runes</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/String-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/String/codeUnitAt.html">codeUnitAt</a></li>
        <li><a href="dart-core/String/compareTo.html">compareTo</a></li>
        <li><a href="dart-core/String/contains.html">contains</a></li>
        <li><a href="dart-core/String/endsWith.html">endsWith</a></li>
        <li><a href="dart-core/String/indexOf.html">indexOf</a></li>
        <li><a href="dart-core/String/lastIndexOf.html">lastIndexOf</a></li>
        <li><a href="dart-core/String/padLeft.html">padLeft</a></li>
        <li><a href="dart-core/String/padRight.html">padRight</a></li>
        <li><a href="dart-core/String/replaceAll.html">replaceAll</a></li>
        <li><a href="dart-core/String/replaceAllMapped.html">replaceAllMapped</a></li>
        <li><a href="dart-core/String/replaceFirst.html">replaceFirst</a></li>
        <li><a href="dart-core/String/replaceFirstMapped.html">replaceFirstMapped</a></li>
        <li><a href="dart-core/String/replaceRange.html">replaceRange</a></li>
        <li><a href="dart-core/String/split.html">split</a></li>
        <li><a href="dart-core/String/splitMapJoin.html">splitMapJoin</a></li>
        <li><a href="dart-core/String/startsWith.html">startsWith</a></li>
        <li><a href="dart-core/String/substring.html">substring</a></li>
        <li><a href="dart-core/String/toLowerCase.html">toLowerCase</a></li>
        <li><a href="dart-core/String/toUpperCase.html">toUpperCase</a></li>
        <li><a href="dart-core/String/trim.html">trim</a></li>
        <li><a href="dart-core/String/trimLeft.html">trimLeft</a></li>
        <li><a href="dart-core/String/trimRight.html">trimRight</a></li>
        <li class="inherited"><a href="dart-core/Pattern/allMatches.html">allMatches</a></li>
        <li class="inherited"><a href="dart-core/Pattern/matchAsPrefix.html">matchAsPrefix</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title"><a href="dart-core/String-class.html#operators">Operators</a></li>
        <li><a href="dart-core/String/operator_multiply.html">operator *</a></li>
        <li><a href="dart-core/String/operator_plus.html">operator +</a></li>
        <li><a href="dart-core/String/operator_equals.html">operator ==</a></li>
        <li><a href="dart-core/String/operator_get.html">operator []</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">split</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>&gt;</span></span>
            <span class="name ">split</span>
(<wbr><span class="parameter" id="split-param-pattern"><span class="type-annotation"><a href="dart-core/Pattern-class.html">Pattern</a></span> <span class="parameter-name">pattern</span></span>)
      
    </section>
    <section class="desc markdown">
      <p>Splits the string at matches of <code>pattern</code> and returns a list of substrings.</p>
<p>Finds all the matches of <code>pattern</code> in this string,
as by using <a href="dart-core/Pattern/allMatches.html">Pattern.allMatches</a>,
and returns the list of the substrings between the matches,
before the first match, and after the last match.</p>
<pre class="language-dart"><code class="language-dart">var string = "Hello world!";
string.split(" ");                      // ["Hello", "world!"];
</code></pre>
<p>If the pattern doesn't match this string at all,
the result is always a list containing only the original string.</p>
<p>If the <code>pattern</code> is a <a href="dart-core/String-class.html">String</a>, then it's always the case that:</p>
<pre class="language-dart"><code class="language-dart">string.split(pattern).join(pattern) == string
</code></pre>
<p>If the first match is an empty match at the start of the string,
the empty substring before it is not included in the result.
If the last match is an empty match at the end of the string,
the empty substring after it is not included in the result.
If a match is empty, and it immediately follows a previous
match (it starts at the position where the previous match ended),
then the empty substring between the two matches is not
included in the result.</p>
<pre class="language-dart"><code class="language-dart">var string = "abba";
var re = RegExp(r"b*");
// re.allMatches(string) will find four matches:
// * empty match before first "a".
// * match of "bb"
// * empty match after "bb", before second "a"
// * empty match after second "a".
print(string.split(re));  // ["a", "a"]
</code></pre>
<p>A non-empty match at the start or end of the string, or after another
match, is not treated specially, and will introduce empty substrings
in the result:</p>
<pre class="language-dart"><code class="language-dart">var string = "abbaa";
string.split("a"); // ["", "bb", "", ""]
</code></pre>
<p>If this string is the empty string, the result is an empty list
if <code>pattern</code> matches the empty string, since the empty string
before and after the first-and-last empty match are not included.
(It is still a list containing the original empty string <code>[""]</code>
if the pattern doesn't match).</p>
<pre class="language-dart"><code class="language-dart">var string = "";
string.split("");                       // []
string.split("a");                      // [""]
</code></pre>
<p>Splitting with an empty pattern splits the string into single-code unit
strings.</p>
<pre class="language-dart"><code class="language-dart">var string = "Pub";
string.split("");                       // ["P", "u", "b"]
// Same as:
[for (var unit in string.codeUnits)
    String.fromCharCode(unit)]          // ["P", "u", "b"]
</code></pre>
<p>Splitting happens at UTF-16 code unit boundaries,
and not at rune (Unicode code point) boundaries:</p>
<pre class="language-dart"><code class="language-dart">// String made up of two code units, but one rune.
string = '\u{1D11E}';
string.split('')  // ["\ud834", "\udd1e"] - 2 unpaired surrogate values
</code></pre>
<p>To get a list of strings containing the individual runes of a string,
you should not use split.
You can instead get a string for each rune as follows:</p>
<pre class="language-dart"><code class="language-dart">[for (var run in string.runes) String.fromCharCode(rune)]
</code></pre>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">List&lt;String&gt; split(Pattern pattern);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
