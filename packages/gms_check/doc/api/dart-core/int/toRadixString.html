<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the toRadixString method from the int class, for the Dart programming language.">
  <title>toRadixString method - int class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/int-class.html">int</a></li>
    <li class="self-crumb">toRadixString abstract method</li>
  </ol>
  <div class="self-name">toRadixString</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/int-class.html">int</a></li>
      <li class="self-crumb">toRadixString abstract method</li>
    </ol>
    
    <h5>int class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/int-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/int/int.fromEnvironment.html">fromEnvironment</a></li>
    
        <li class="section-title">
            <a href="dart-core/int-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/int/bitLength.html">bitLength</a></li>
        <li><a href="dart-core/int/isEven.html">isEven</a></li>
        <li><a href="dart-core/int/isOdd.html">isOdd</a></li>
        <li><a href="dart-core/int/sign.html">sign</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/num/isFinite.html">isFinite</a></li>
        <li class="inherited"><a href="dart-core/num/isInfinite.html">isInfinite</a></li>
        <li class="inherited"><a href="dart-core/num/isNaN.html">isNaN</a></li>
        <li class="inherited"><a href="dart-core/num/isNegative.html">isNegative</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/int-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/int/abs.html">abs</a></li>
        <li><a href="dart-core/int/ceil.html">ceil</a></li>
        <li><a href="dart-core/int/ceilToDouble.html">ceilToDouble</a></li>
        <li><a href="dart-core/int/floor.html">floor</a></li>
        <li><a href="dart-core/int/floorToDouble.html">floorToDouble</a></li>
        <li><a href="dart-core/int/gcd.html">gcd</a></li>
        <li><a href="dart-core/int/modInverse.html">modInverse</a></li>
        <li><a href="dart-core/int/modPow.html">modPow</a></li>
        <li><a href="dart-core/int/round.html">round</a></li>
        <li><a href="dart-core/int/roundToDouble.html">roundToDouble</a></li>
        <li><a href="dart-core/int/toRadixString.html">toRadixString</a></li>
        <li><a href="dart-core/int/toSigned.html">toSigned</a></li>
        <li><a href="dart-core/int/toString.html">toString</a></li>
        <li><a href="dart-core/int/toUnsigned.html">toUnsigned</a></li>
        <li><a href="dart-core/int/truncate.html">truncate</a></li>
        <li><a href="dart-core/int/truncateToDouble.html">truncateToDouble</a></li>
        <li class="inherited"><a href="dart-core/num/clamp.html">clamp</a></li>
        <li class="inherited"><a href="dart-core/num/compareTo.html">compareTo</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/num/remainder.html">remainder</a></li>
        <li class="inherited"><a href="dart-core/num/toDouble.html">toDouble</a></li>
        <li class="inherited"><a href="dart-core/num/toInt.html">toInt</a></li>
        <li class="inherited"><a href="dart-core/num/toStringAsExponential.html">toStringAsExponential</a></li>
        <li class="inherited"><a href="dart-core/num/toStringAsFixed.html">toStringAsFixed</a></li>
        <li class="inherited"><a href="dart-core/num/toStringAsPrecision.html">toStringAsPrecision</a></li>
    
        <li class="section-title"><a href="dart-core/int-class.html#operators">Operators</a></li>
        <li><a href="dart-core/int/operator_bitwise_and.html">operator &</a></li>
        <li><a href="dart-core/int/operator_shift_left.html">operator <<</a></li>
        <li><a href="dart-core/int/operator_greater.html">operator ></a></li>
        <li><a href="dart-core/int/operator_shift_right.html">operator >></a></li>
        <li><a href="dart-core/int/operator_shift_right.html">operator >></a></li>
        <li><a href="dart-core/int/operator_bitwise_exclusive_or.html">operator ^</a></li>
        <li><a href="dart-core/int/operator_unary_minus.html">operator unary-</a></li>
        <li><a href="dart-core/int/operator_bitwise_or.html">operator |</a></li>
        <li><a href="dart-core/int/operator_bitwise_negate.html">operator ~</a></li>
        <li class="inherited"><a href="dart-core/num/operator_modulo.html">operator %</a></li>
        <li class="inherited"><a href="dart-core/num/operator_multiply.html">operator *</a></li>
        <li class="inherited"><a href="dart-core/num/operator_plus.html">operator +</a></li>
        <li class="inherited"><a href="dart-core/num/operator_minus.html">operator -</a></li>
        <li class="inherited"><a href="dart-core/num/operator_divide.html">operator /</a></li>
        <li class="inherited"><a href="dart-core/num/operator_less.html">operator <</a></li>
        <li class="inherited"><a href="dart-core/num/operator_less_equal.html">operator <=</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
        <li class="inherited"><a href="dart-core/num/operator_greater_equal.html">operator >=</a></li>
        <li class="inherited"><a href="dart-core/num/operator_truncate_divide.html">operator ~/</a></li>
    
    
        <li class="section-title"><a href="dart-core/int-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-core/int/parse.html">parse</a></li>
        <li><a href="dart-core/int/tryParse.html">tryParse</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">toRadixString</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/String-class.html">String</a></span>
            <span class="name ">toRadixString</span>
(<wbr><span class="parameter" id="toRadixString-param-radix"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">radix</span></span>)
      
    </section>
    <section class="desc markdown">
      <p>Converts <a href="dart-core/int-class.html">this</a> to a string representation in the given <code>radix</code>.</p>
<p>In the string representation, lower-case letters are used for digits above
'9', with 'a' being 10 an 'z' being 35.</p>
<p>The <code>radix</code> argument must be an integer in the range 2 to 36.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">String toRadixString(int radix);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
