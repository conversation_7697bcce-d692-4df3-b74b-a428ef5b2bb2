<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the queryParametersAll property from the Uri class, for the Dart programming language.">
  <title>queryParametersAll property - Uri class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/Uri-class.html">Uri</a></li>
    <li class="self-crumb">queryParametersAll property</li>
  </ol>
  <div class="self-name">queryParametersAll</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/Uri-class.html">Uri</a></li>
      <li class="self-crumb">queryParametersAll property</li>
    </ol>
    
    <h5>Uri class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/Uri-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/Uri/Uri.html">Uri</a></li>
        <li><a href="dart-core/Uri/Uri.dataFromBytes.html">dataFromBytes</a></li>
        <li><a href="dart-core/Uri/Uri.dataFromString.html">dataFromString</a></li>
        <li><a href="dart-core/Uri/Uri.directory.html">directory</a></li>
        <li><a href="dart-core/Uri/Uri.file.html">file</a></li>
        <li><a href="dart-core/Uri/Uri.http.html">http</a></li>
        <li><a href="dart-core/Uri/Uri.https.html">https</a></li>
    
        <li class="section-title">
            <a href="dart-core/Uri-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/Uri/authority.html">authority</a></li>
        <li><a href="dart-core/Uri/data.html">data</a></li>
        <li><a href="dart-core/Uri/fragment.html">fragment</a></li>
        <li><a href="dart-core/Uri/hasAbsolutePath.html">hasAbsolutePath</a></li>
        <li><a href="dart-core/Uri/hasAuthority.html">hasAuthority</a></li>
        <li><a href="dart-core/Uri/hasEmptyPath.html">hasEmptyPath</a></li>
        <li><a href="dart-core/Uri/hasFragment.html">hasFragment</a></li>
        <li><a href="dart-core/Uri/hashCode.html">hashCode</a></li>
        <li><a href="dart-core/Uri/hasPort.html">hasPort</a></li>
        <li><a href="dart-core/Uri/hasQuery.html">hasQuery</a></li>
        <li><a href="dart-core/Uri/hasScheme.html">hasScheme</a></li>
        <li><a href="dart-core/Uri/host.html">host</a></li>
        <li><a href="dart-core/Uri/isAbsolute.html">isAbsolute</a></li>
        <li><a href="dart-core/Uri/origin.html">origin</a></li>
        <li><a href="dart-core/Uri/path.html">path</a></li>
        <li><a href="dart-core/Uri/pathSegments.html">pathSegments</a></li>
        <li><a href="dart-core/Uri/port.html">port</a></li>
        <li><a href="dart-core/Uri/query.html">query</a></li>
        <li><a href="dart-core/Uri/queryParameters.html">queryParameters</a></li>
        <li><a href="dart-core/Uri/queryParametersAll.html">queryParametersAll</a></li>
        <li><a href="dart-core/Uri/scheme.html">scheme</a></li>
        <li><a href="dart-core/Uri/userInfo.html">userInfo</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/Uri-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/Uri/isScheme.html">isScheme</a></li>
        <li><a href="dart-core/Uri/normalizePath.html">normalizePath</a></li>
        <li><a href="dart-core/Uri/removeFragment.html">removeFragment</a></li>
        <li><a href="dart-core/Uri/replace.html">replace</a></li>
        <li><a href="dart-core/Uri/resolve.html">resolve</a></li>
        <li><a href="dart-core/Uri/resolveUri.html">resolveUri</a></li>
        <li><a href="dart-core/Uri/toFilePath.html">toFilePath</a></li>
        <li><a href="dart-core/Uri/toString.html">toString</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
        <li class="section-title"><a href="dart-core/Uri-class.html#operators">Operators</a></li>
        <li><a href="dart-core/Uri/operator_equals.html">operator ==</a></li>
    
        <li class="section-title"><a href="dart-core/Uri-class.html#static-properties">Static properties</a></li>
        <li><a href="dart-core/Uri/base.html">base</a></li>
    
        <li class="section-title"><a href="dart-core/Uri-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-core/Uri/decodeComponent.html">decodeComponent</a></li>
        <li><a href="dart-core/Uri/decodeFull.html">decodeFull</a></li>
        <li><a href="dart-core/Uri/decodeQueryComponent.html">decodeQueryComponent</a></li>
        <li><a href="dart-core/Uri/encodeComponent.html">encodeComponent</a></li>
        <li><a href="dart-core/Uri/encodeFull.html">encodeFull</a></li>
        <li><a href="dart-core/Uri/encodeQueryComponent.html">encodeQueryComponent</a></li>
        <li><a href="dart-core/Uri/parse.html">parse</a></li>
        <li><a href="dart-core/Uri/parseIPv4Address.html">parseIPv4Address</a></li>
        <li><a href="dart-core/Uri/parseIPv6Address.html">parseIPv6Address</a></li>
        <li><a href="dart-core/Uri/splitQueryString.html">splitQueryString</a></li>
        <li><a href="dart-core/Uri/tryParse.html">tryParse</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-property">queryParametersAll</span> property</h1></div>


        <section id="getter">
        
        <section class="multi-line-signature">
          <span class="returntype"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>, <span class="type-parameter"><a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>&gt;</span></span>&gt;</span></span>
                  <span class="name ">queryParametersAll</span>
          
</section>
        
                <section class="desc markdown">
          <p>Returns the URI query split into a map according to the rules
specified for FORM post in the <a href="http://www.w3.org/TR/REC-html40/interact/forms.html#h-17.13.4" title="HTML 4.01 section 17.13.4">HTML 4.01 specification section
17.13.4</a>.</p>
<p>Each key and value in the resulting map has been decoded. If there is no
query the map is empty.</p>
<p>Keys are mapped to lists of their values. If a key occurs only once,
its value is a singleton list. If a key occurs with no value, the
empty string is used as the value for that occurrence.</p>
<p>The map and the lists it contains are unmodifiable.</p>
        </section>
                <section class="summary source-code" id="source">
          <h2><span>Implementation</span></h2>
          <pre class="language-dart"><code class="language-dart">Map&lt;String, List&lt;String&gt;&gt; get queryParametersAll;</code></pre>
        </section>
</section>
        
  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
