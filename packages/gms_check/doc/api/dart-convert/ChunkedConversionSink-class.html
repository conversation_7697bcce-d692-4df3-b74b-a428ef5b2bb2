<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the ChunkedConversionSink class from the dart:convert library, for the Dart programming language.">
  <title>ChunkedConversionSink class - dart:convert library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
    <li class="self-crumb">ChunkedConversionSink<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span> abstract class</li>
  </ol>
  <div class="self-name">ChunkedConversionSink</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
      <li class="self-crumb">ChunkedConversionSink<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span> abstract class</li>
    </ol>
    
    <h5>dart:convert library</h5>
    <ol>
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#classes">Classes</a></li>
      <li><a href="dart-convert/AsciiCodec-class.html">AsciiCodec</a></li>
      <li><a href="dart-convert/AsciiDecoder-class.html">AsciiDecoder</a></li>
      <li><a href="dart-convert/AsciiEncoder-class.html">AsciiEncoder</a></li>
      <li><a href="dart-convert/Base64Codec-class.html">Base64Codec</a></li>
      <li><a href="dart-convert/Base64Decoder-class.html">Base64Decoder</a></li>
      <li><a href="dart-convert/Base64Encoder-class.html">Base64Encoder</a></li>
      <li><a href="dart-convert/ByteConversionSink-class.html">ByteConversionSink</a></li>
      <li><a href="dart-convert/ByteConversionSinkBase-class.html">ByteConversionSinkBase</a></li>
      <li><a href="dart-convert/ChunkedConversionSink-class.html">ChunkedConversionSink</a></li>
      <li><a href="dart-convert/ClosableStringSink-class.html">ClosableStringSink</a></li>
      <li><a href="dart-convert/Codec-class.html">Codec</a></li>
      <li><a href="dart-convert/Converter-class.html">Converter</a></li>
      <li><a href="dart-convert/Encoding-class.html">Encoding</a></li>
      <li><a href="dart-convert/HtmlEscape-class.html">HtmlEscape</a></li>
      <li><a href="dart-convert/HtmlEscapeMode-class.html">HtmlEscapeMode</a></li>
      <li><a href="dart-convert/JsonCodec-class.html">JsonCodec</a></li>
      <li><a href="dart-convert/JsonDecoder-class.html">JsonDecoder</a></li>
      <li><a href="dart-convert/JsonEncoder-class.html">JsonEncoder</a></li>
      <li><a href="dart-convert/JsonUtf8Encoder-class.html">JsonUtf8Encoder</a></li>
      <li><a href="dart-convert/Latin1Codec-class.html">Latin1Codec</a></li>
      <li><a href="dart-convert/Latin1Decoder-class.html">Latin1Decoder</a></li>
      <li><a href="dart-convert/Latin1Encoder-class.html">Latin1Encoder</a></li>
      <li><a href="dart-convert/LineSplitter-class.html">LineSplitter</a></li>
      <li><a href="dart-convert/StringConversionSink-class.html">StringConversionSink</a></li>
      <li><a href="dart-convert/StringConversionSinkBase-class.html">StringConversionSinkBase</a></li>
      <li><a href="dart-convert/StringConversionSinkMixin-class.html">StringConversionSinkMixin</a></li>
      <li><a href="dart-convert/Utf8Codec-class.html">Utf8Codec</a></li>
      <li><a href="dart-convert/Utf8Decoder-class.html">Utf8Decoder</a></li>
      <li><a href="dart-convert/Utf8Encoder-class.html">Utf8Encoder</a></li>
    
    
    
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#constants">Constants</a></li>
      <li><a href="dart-convert/ascii-constant.html">ascii</a></li>
      <li><a href="dart-convert/base64-constant.html">base64</a></li>
      <li><a href="dart-convert/base64Url-constant.html">base64Url</a></li>
      <li><a href="dart-convert/htmlEscape-constant.html">htmlEscape</a></li>
      <li><a href="dart-convert/json-constant.html">json</a></li>
      <li><a href="dart-convert/latin1-constant.html">latin1</a></li>
      <li><a href="dart-convert/unicodeBomCharacterRune-constant.html">unicodeBomCharacterRune</a></li>
      <li><a href="dart-convert/unicodeReplacementCharacterRune-constant.html">unicodeReplacementCharacterRune</a></li>
      <li><a href="dart-convert/utf8-constant.html">utf8</a></li>
    
    
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#functions">Functions</a></li>
      <li><a href="dart-convert/base64Decode.html">base64Decode</a></li>
      <li><a href="dart-convert/base64Encode.html">base64Encode</a></li>
      <li><a href="dart-convert/base64UrlEncode.html">base64UrlEncode</a></li>
      <li><a href="dart-convert/jsonDecode.html">jsonDecode</a></li>
      <li><a href="dart-convert/jsonEncode.html">jsonEncode</a></li>
    
    
    
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-convert/JsonCyclicError-class.html">JsonCyclicError</a></li>
      <li><a href="dart-convert/JsonUnsupportedObjectError-class.html">JsonUnsupportedObjectError</a></li>
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">ChunkedConversionSink&lt;<wbr><span class="type-parameter">T</span>&gt;</span> class </h1></div>

    <section class="desc markdown">
      <p>A <a href="dart-convert/ChunkedConversionSink-class.html">ChunkedConversionSink</a> is used to transmit data more efficiently between
two converters during chunked conversions.</p>
<p>The basic <code>ChunkedConversionSink</code> is just a <a href="dart-core/Sink-class.html">Sink</a>, and converters should
work with a plain <code>Sink</code>, but may work more efficiently with certain
specialized types of <code>ChunkedConversionSink</code>.</p>
<p>It is recommended that implementations of <code>ChunkedConversionSink</code> extend
this class, to inherit any further methods that may be added to the class.</p>
    </section>
    
    <section>
      <dl class="dl-horizontal">

        <dt>Implemented types</dt>
        <dd>
          <ul class="comma-separated clazz-relationships">
            <li><a href="dart-core/Sink-class.html">Sink</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></li>
          </ul>
        </dd>


        <dt>Implementers</dt>
        <dd><ul class="comma-separated clazz-relationships">
          <li><a href="dart-convert/ByteConversionSink-class.html">ByteConversionSink</a></li>
          <li><a href="dart-convert/StringConversionSink-class.html">StringConversionSink</a></li>
        </ul></dd>


      </dl>
    </section>

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="ChunkedConversionSink" class="callable">
          <span class="name"><a href="dart-convert/ChunkedConversionSink/ChunkedConversionSink.html">ChunkedConversionSink</a></span><span class="signature">()</span>
        </dt>
        <dd>
          
        </dd>
        <dt id="ChunkedConversionSink.withCallback" class="callable">
          <span class="name"><a href="dart-convert/ChunkedConversionSink/ChunkedConversionSink.withCallback.html">ChunkedConversionSink.withCallback</a></span><span class="signature">(<span class="parameter" id="withCallback-param-callback"><span class="type-annotation">void</span> <span class="parameter-name">callback</span>(<span class="parameter" id="param-accumulated"><span class="type-annotation"><a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">accumulated</span></span>)</span>)</span>
        </dt>
        <dd>
          
          <div class="constructor-modifier features">factory</div>
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="add" class="callable">
          <span class="name"><a href="dart-convert/ChunkedConversionSink/add.html">add</a></span><span class="signature">(<wbr><span class="parameter" id="add-param-chunk"><span class="type-annotation">T</span> <span class="parameter-name">chunk</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Adds chunked data to this sink. <a href="dart-convert/ChunkedConversionSink/add.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="close" class="callable">
          <span class="name"><a href="dart-convert/ChunkedConversionSink/close.html">close</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Closes the sink. <a href="dart-convert/ChunkedConversionSink/close.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-core/Object/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-core/Object/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-convert/ChunkedConversionSink-class.html#constructors">Constructors</a></li>
      <li><a href="dart-convert/ChunkedConversionSink/ChunkedConversionSink.html">ChunkedConversionSink</a></li>
      <li><a href="dart-convert/ChunkedConversionSink/ChunkedConversionSink.withCallback.html">withCallback</a></li>
    
      <li class="section-title inherited">
        <a href="dart-convert/ChunkedConversionSink-class.html#instance-properties">Properties</a>
      </li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-convert/ChunkedConversionSink-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-convert/ChunkedConversionSink/add.html">add</a></li>
      <li><a href="dart-convert/ChunkedConversionSink/close.html">close</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-convert/ChunkedConversionSink-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
