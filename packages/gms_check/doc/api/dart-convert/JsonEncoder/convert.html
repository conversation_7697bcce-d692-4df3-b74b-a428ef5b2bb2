<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the convert method from the JsonEncoder class, for the Dart programming language.">
  <title>convert method - JsonEncoder class - dart:convert library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
    <li><a href="dart-convert/JsonEncoder-class.html">JsonEncoder</a></li>
    <li class="self-crumb">convert method</li>
  </ol>
  <div class="self-name">convert</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
      <li><a href="dart-convert/JsonEncoder-class.html">JsonEncoder</a></li>
      <li class="self-crumb">convert method</li>
    </ol>
    
    <h5>JsonEncoder class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-convert/JsonEncoder-class.html#constructors">Constructors</a></li>
        <li><a href="dart-convert/JsonEncoder/JsonEncoder.html">JsonEncoder</a></li>
        <li><a href="dart-convert/JsonEncoder/JsonEncoder.withIndent.html">withIndent</a></li>
    
        <li class="section-title">
            <a href="dart-convert/JsonEncoder-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-convert/JsonEncoder/indent.html">indent</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-convert/JsonEncoder-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-convert/JsonEncoder/bind.html">bind</a></li>
        <li><a href="dart-convert/JsonEncoder/convert.html">convert</a></li>
        <li><a href="dart-convert/JsonEncoder/fuse.html">fuse</a></li>
        <li><a href="dart-convert/JsonEncoder/startChunkedConversion.html">startChunkedConversion</a></li>
        <li class="inherited"><a href="dart-convert/Converter/cast.html">cast</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-convert/JsonEncoder-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">convert</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/String-class.html">String</a></span>
            <span class="name ">convert</span>
(<wbr><span class="parameter" id="convert-param-object"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">object</span></span>)
      <div class="features">override</div>
    </section>
    <section class="desc markdown">
      <p>Converts <code>object</code> to a JSON <a href="dart-core/String-class.html">String</a>.</p>
<p>Directly serializable values are <a href="dart-core/num-class.html">num</a>, <a href="dart-core/String-class.html">String</a>, <a href="dart-core/bool-class.html">bool</a>, and <a href="dart-core/Null-class.html">Null</a>, as
well as some <a href="dart-core/List-class.html">List</a> and <a href="dart-core/Map-class.html">Map</a> values. For <a href="dart-core/List-class.html">List</a>, the elements must all be
serializable. For <a href="dart-core/Map-class.html">Map</a>, the keys must be <a href="dart-core/String-class.html">String</a> and the values must be
serializable.</p>
<p>If a value of any other type is attempted to be serialized, the
<code>toEncodable</code> function provided in the constructor is called with the value
as argument. The result, which must be a directly serializable value, is
serialized instead of the original value.</p>
<p>If the conversion throws, or returns a value that is not directly
serializable, a <a href="dart-convert/JsonUnsupportedObjectError-class.html">JsonUnsupportedObjectError</a> exception is thrown.
If the call throws, the error is caught and stored in the
<a href="dart-convert/JsonUnsupportedObjectError-class.html">JsonUnsupportedObjectError</a>'s <code>cause</code> field.</p>
<p>If a <a href="dart-core/List-class.html">List</a> or <a href="dart-core/Map-class.html">Map</a> contains a reference to itself, directly or through
other lists or maps, it cannot be serialized and a <a href="dart-convert/JsonCyclicError-class.html">JsonCyclicError</a> is
thrown.</p>
<p><code>object</code> should not change during serialization.</p>
<p>If an object is serialized more than once, <a href="dart-convert/JsonEncoder/convert.html">convert</a> may cache the text
for it. In other words, if the content of an object changes after it is
first serialized, the new values may not be reflected in the result.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">String convert(Object? object) =&gt;
    _JsonStringStringifier.stringify(object, _toEncodable, indent);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
