{"kind": "collectionType", "collectionName": "products", "info": {"singularName": "product", "pluralName": "products", "displayName": "Product", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "description": {"type": "text"}, "images": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "price": {"type": "decimal"}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::sub-category.sub-category", "inversedBy": "products"}, "ratings": {"type": "relation", "relation": "oneToMany", "target": "api::rating.rating", "mappedBy": "product"}, "stock": {"type": "integer"}, "featured": {"type": "boolean", "default": false}, "is_service": {"type": "boolean", "default": false}, "sale_price": {"type": "decimal"}, "cities": {"type": "relation", "relation": "oneToMany", "target": "api::city.city"}, "session_time": {"type": "integer"}, "sort": {"unique": true, "type": "integer", "min": 1}}}