import 'dart:async';
import 'dart:io' show HttpClient, HttpOverrides, SecurityContext;

import 'package:flutter/foundation.dart' as foundation;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flux_firebase/index.dart';
import 'package:fstore/frameworks/strapi/services/strapi_service.dart';
import 'package:gms_check/gms_check.dart';
import 'package:provider/provider.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:universal_html/html.dart' as html;
import 'package:universal_platform/universal_platform.dart';
import 'app.dart';
import 'common/config.dart' as AppConsts;
import 'common/config.dart';
import 'common/constants.dart';
import 'common/tools/biometrics_tools.dart';
import 'common/utils/change_web_data.dart';
import 'data/boxes.dart';
import 'env.dart';
import 'modules/analytics/analytics.dart';
import 'modules/meta_seo/meta_seo_service.dart';
import 'modules/webview/index.dart';
import 'services/dependency_injection.dart';
import 'services/get_storage_service.dart';
import 'services/locale_service.dart';
import 'services/services.dart';

// * Deploy Website ==================
//! flutter build web --release
//! firebase deploy --only hosting

//! flutter run -d chrome --web-renderer html

//! flutter run -d chrome --web-browser-flag "--disable-web-security"

// * Deploy to git & Vercel Hosting
//! git commit --amend --author="Idea2App <<EMAIL>>"
//! GIT_SSH_COMMAND="ssh -i ~/.ssh/id_rsa_idea2app" git push --force origin main

//! ================================================
Future<void> _firebaseMessagingBackgroundHandler(dynamic message) async {
  await Firebase.initializeApp();
  printLog('Handling a background message ${message.messageId}');
}

void _setupApplication() {
  Configurations().setConfigurationValues(environment);

  /// Fix issue android sdk version 22 can not run the app.
  if (UniversalPlatform.isAndroid) {
    SecurityContext.defaultContext
        .setTrustedCertificatesBytes(Uint8List.fromList(isrgRootX1.codeUnits));
  }

  /// Support Webview (iframe) for Flutter Web. Requires this below header.
  /// Content-Security-Policy: frame-ancestors 'self' *.yourdomain.com
  registerWebViewWebImplementation();

  /// Hide status bar for splash screen
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.leanBack);

  Provider.debugCheckInvalidValueType = null;

  LicenseRegistry.addLicense(() async* {
    final license = await rootBundle.loadString('google_fonts/OFL.txt');
    yield LicenseEntryWithLineBreaks(['google_fonts'], license);
  });

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (cert, host, port) => true;
  }
}

void main() {
  printLog('[main] ===== START main.dart =======');

  // It is required to add the following to run the meta_seo package correctly
  // before the running of the Flutter app
  if (foundation.kIsWeb) {
    MetaSeoService.config();
  }

  HttpOverrides.global = MyHttpOverrides();

  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    /// Call the setup for the application.
    _setupApplication();

    /// get language code default
    var languageCode = kAdvanceConfig.defaultLanguage;

    /// Init Hive boxes.
    await initBoxes();

    if (!foundation.kIsWeb) {
      /// Enable network traffic logging.
      HttpClient.enableTimelineLogging = !foundation.kReleaseMode;

      /// Lock portrait mode.
      unawaited(SystemChrome.setPreferredOrientations(
          [DeviceOrientation.portraitUp]));
    } else {
      /// Set webProxy for web
      HttpClientSetting.webProxy = Configurations.webProxy;
    }

    await GmsCheck().checkGmsAvailability(enableLog: foundation.kDebugMode);

    try {
      if (isMobile) {
        /// Init Firebase settings due to version 0.5.0+ requires to.
        /// Use await to prevent any usage until the initialization is completed.
        await Services().firebase.init();
        await Configurations().loadRemoteConfig();
        await BiometricsTools.instance.init();
      }
    } catch (e) {
      printLog(e);
      printLog('🔴 Firebase init issue');
    }

    await DependencyInjection.inject();

    Services().setAppConfig(serverConfig,
        ignoreInitCart: (Configurations.multiSiteConfigs?.isNotEmpty ?? false));

    Analytics.instance.init();

    if (isMobile && kAdvanceConfig.autoDetectLanguage) {
      final lang = SettingsBox().languageCode;

      if (lang?.isEmpty ?? true) {
        languageCode = await LocaleService().getDeviceLanguage();
      } else {
        languageCode = lang.toString();
      }
    }

    ResponsiveSizingConfig.instance.setCustomBreakpoints(
        const ScreenBreakpoints(desktop: 1000, tablet: 600, watch: 100));

    // if (!foundation.kIsWeb) {
    //   final fcm = FirebaseMessaging.instance;
    //
    //   unawaited(fcm.subscribeToTopic('$vendorId-Customer'));
    // }

    await GetStorageService.init();

    if (foundation.kIsWeb) {
      final url = Uri.parse(html.window.location.href);
      final vendorIdParam = Uri.base.queryParameters['vendor'];

      if (isIdea2App) {
        if (url.pathSegments.isNotEmpty) {
          vendorBusinessName = url.pathSegments.first;
        } else {
          vendorBusinessName = vendorIdParam ?? vendorBusinessName;
        }
      }
    }

    if (foundation.kIsWeb) {
      currentVendor = await StrapiService.getCurrentVendor();

      setWebFavIcon(currentVendor?.logoUrl);
    }

    unawaited(StrapiService.getPromoCodes());

    printLog('[CurrentVendor] ===== ${currentVendor.toString()}');

    runApp(
      App(
        languageCode: languageCode,
      ),
    );
  }, printError);
}
