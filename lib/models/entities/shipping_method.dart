import 'package:flutter/material.dart';

import '../../common/constants.dart';
import '../serializers/shipping.dart';
import 'country_state.dart';

class ShippingMethod {
  String? id;
  String? title;
  String? description;
  double? cost;
  double? shippingTax;
  double? minAmount;
  String? classCost;
  String? methodId;
  List<(CountryState? city, double? cost, bool? freeShipping)>? citiesCost;
  String? methodTitle;

  Map<String, dynamic> toJson() {
    return {'id': id, 'title': title, 'description': description, 'cost': cost};
  }

  ShippingMethod.fromJson(Map parsedJson) {
    try {
      id = '${parsedJson['id']}';
      title = parsedJson['label'];
      methodId = parsedJson['method_id'];
      methodTitle = parsedJson['label'];
      cost = double.tryParse("${parsedJson["cost"] ?? '0'}") ?? 0;
      shippingTax = double.parse("${parsedJson["shipping_tax"]}");

      if (parsedJson['cities_cost'] != null) {
        citiesCost = [];

        for (var item in parsedJson['cities_cost']) {
          citiesCost?.add((
            item['city'] != null
                ? CountryState.fromStrapiJson(item['city'])
                : null,
            double.tryParse(
              '${item['cost']}',
            ),
            item['free_shipping'] ?? false,
          ));
        }
      }
    } catch (e) {
      printLog('error parsing Shipping method');
    }
  }

  ShippingMethod.fromStrapi(Map<String, dynamic> parsedJson) {
    printLog('afasfafaf ${parsedJson['cities_cost']}');
    var model = SerializerShipping.fromJson(parsedJson);
    try {
      id = model.id.toString();
      title = model.title;
      description = model.description;
      cost = model.cost?.toDouble() ?? 0;

      try {
        if (parsedJson['cities_cost'] != null) {
          citiesCost = [];

          for (var item in parsedJson['cities_cost']) {
            printLog('iajfiasflisalf ${item}');
            citiesCost?.add((
              item['city'] != null
                  ? CountryState.fromStrapiJson(item['city'])
                  : null,
              double.tryParse('${item['cost']}'),
              item['free_shipping'] ?? false,
            ));
          }
        }
      } catch (e, trace) {
        printLog('error parsing cities_cost on Shipping method: $e, $trace');
      }
    } on Exception catch (e, trace) {
      debugPrint(
          'Error on Strapi shipping model: ${e.toString()}, trace: ${trace.toString()}');
    }
  }

  @override
  String toString() {
    return 'ShippingMethod{id: $id, title: $title, description: $description, cost: $cost, minAmount: $minAmount, classCost: $classCost, methodId: $methodId, methodTitle: $methodTitle}';
  }
}
