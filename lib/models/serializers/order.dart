import 'package:json_annotation/json_annotation.dart';

import '../order/product_item.dart';
import 'address.dart';
import 'payment.dart';
import 'shipping.dart';
import 'user.dart';

part 'order.g.dart';

@JsonSerializable()
class SerializerOrder {
  int? id;
  String? documentId;
  @JsonKey(name: 'createdAt')
  String? createdAt;
  String? orderId;
  double? total;
  double? discount;
  final num? deliveryCost;
  SerializerUser? user;
  SerializerShipping? shipping;
  AddressModel? address;
  SerializerPayment? payment;

  // List<(SerializerProduct product, int productQuantity)>? products;
  List<ProductQuantityModel>? products;

  SerializerOrder(
      {this.id,
      this.createdAt,
      this.total,
      this.discount,
      this.user,
      this.orderId,
      this.shipping,
      this.deliveryCost,
      this.payment,
      this.documentId,
      this.address,
      this.products});

  factory SerializerOrder.fromJson(Map<String, dynamic> json) {
    // final productWithQuantityList = json['products_quantity'] as List<dynamic>?;
    final productWithQuantityList = json['products_quantity'] ?? [];

    final productList = List<ProductQuantityModel>.from(
      productWithQuantityList.map(
        (product) => ProductQuantityModel.fromJson(product),
      ),
    );

    // final productWithQuantity = productWithQuantityList?.map((e) {
    //   final product = e['product'] as Map<String, dynamic>;
    //   final quantity = e['quantity'] as int;
    //   return (SerializerProduct.fromJson(product), quantity);
    // }).toList();

    final address = json['address'] == null
        ? null
        : AddressModel.fromJson(json['address'] as Map<String, dynamic>);

    final shipping = json['shipping'] == null
        ? null
        : SerializerShipping.fromJson(json['shipping'] as Map<String, dynamic>);

    final deliveryCostFee = json['delivery_cost'] ?? shipping?.cost ?? 0;

    return SerializerOrder(
        id: json['id'] as int?,
        orderId: json['order_id']?.toString(),
        documentId: json['documentId'] as String?,
        createdAt: json['createdAt'] as String?,
        total: (json['total'] as num?)?.toDouble(),
        discount: (json['discount'] as num?)?.toDouble(),
        user: json['user'] == null
            ? null
            : SerializerUser.fromJson(json['user'] as Map<String, dynamic>),
        shipping: shipping,
        payment: json['payment'] == null
            ? null
            : SerializerPayment.fromJson(
                json['payment'] as Map<String, dynamic>),
        products: productList,
        deliveryCost: deliveryCostFee,
        address: address

        // (json['products'] as List<dynamic>?)
        //     ?.map((e) => SerializerProduct.fromJson(e as Map<String, dynamic>))
        //     .toList(),
        );
  }

  // _$SerializerOrderFromJson(json);

  Map<String, dynamic> toJson() => _$SerializerOrderToJson(this);

  @override
  String toString() =>
      'SerializerPayment { id: $id from user: ${user!.user!.username}';
}
