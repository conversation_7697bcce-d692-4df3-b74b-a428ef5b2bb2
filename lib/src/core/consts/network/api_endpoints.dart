import 'package:xr_helper/xr_helper.dart';

import '../../../screens/auth/models/user_model.dart';

String filteredPhone(
  String phone, {
  bool withOutCountryCode = false,
  removeCountryCode = false,
}) {
  final countryCode = withOutCountryCode || removeCountryCode ? '' : '+972';
  final phoneWithoutCountryCode = removeCountryCode
      ? (phone.startsWith('+972') ? phone.substring(4) : phone)
      : phone;

  if (removeCountryCode) return '0$phoneWithoutCountryCode';

  return '$countryCode${phone.startsWith('0') ? phone.substring(1) : phoneWithoutCountryCode}';
}

class ApiEndpoints {
  //http://barber.ajory.net:1337/api/providers/closest?lat7.37626992925459&lng=-122.0372898504138&service=a3mvwmos00hnkz362yapicui
  // http://barber.ajory.net:1337/api/providers/closest?lat=37.42235126243611&lng=-122.08410043269394&service=a3mvwmos00hnkz362yapicui
  // static const String url = 'http://barber.ajory.net:1337';
  static const String url = 'http://10.0.2.2:1337';
  static const String baseUrl = '$url/api';

  //? Auth
  static const String register = "$baseUrl/auth/local/register";
  static const String login = "$baseUrl/auth/local";

  static const populate = 'pLevel&sort[0]=createdAt:desc';

  static String baseUrlWithPopulate(String url) {
    final mark = url.contains('?') ? '&' : '?';

    return '$baseUrl/$url$mark$populate';
  }

  //? APIs

  static final String users = baseUrlWithPopulate('users');
  static const String updateUser = '$baseUrl/users';
  static final String cities = baseUrlWithPopulate('cities');

  static final String setting = baseUrlWithPopulate('setting');

  static final String providers = baseUrlWithPopulate('providers');

  static String providersByCity(int? cityId) =>
      baseUrlWithPopulate('providers?filters[city][id]=$cityId');

  //providersByCityAndService
  // static String providersByCityAndService(String? serviceName) =>
  //     baseUrlWithPopulate(
  //         'providers?filters[city][name]=${UserModel.currentUser.city?.name}');
  static String providersByCityAndService(String? serviceDocId) =>
      baseUrlWithPopulate(
          // 'providers');
          'providers?filters[city][name]=${UserModel.currentUser.city?.name}'
          '&filters[services][service][documentId][\$eq]=$serviceDocId'); //TODO-fx

  // '&filters[services][name][\$eq]=$serviceName');
  //&filters[services][name][\$eq]=q11ivgsqpehklnnrmh7rsimy

  static String closestProvidersByLatLng(String? lat, String? lng) =>
      '$baseUrl/providers/closest?lat=$lat&lng=$lng';

  // '$baseUrl/providers/closest?lat=$lat&lng=$lng';

  // * Categories ==========================
  static final String categories = baseUrlWithPopulate('categories');

  static final String subCategories = baseUrlWithPopulate('sub-categories');

  // * Products ==========================
  static final String products = baseUrlWithPopulate('products');

  static String filterProducts({
    bool isService = false,
    bool? isFeatured,
  }) =>
      '$products&filters[is_service]=$isService${isFeatured != null ? '&filters[featured]=$isFeatured' : ''}';

  // * Promo Codes ==========================
  static String validatePromoCode(String promo) => baseUrlWithPopulate(
      'promo-codes?filters[active]=true&filters[createdAt][\$lte]=${DateTime.now().formatDateToString}&filters[code]=$promo');

  // * Banners ==========================
  static final String banners = baseUrlWithPopulate('banners');

  // * Orders ==========================
  static final String orders = baseUrlWithPopulate('orders');
  static const String ratings = '$baseUrl/ratings';
}
