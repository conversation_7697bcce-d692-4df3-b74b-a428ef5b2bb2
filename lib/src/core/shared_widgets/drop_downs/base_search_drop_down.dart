part of '../shared_widgets.dart';

const _radius = Radius.circular(12);

class BaseSearchDropDown extends HookWidget {
  final dynamic selectedValue;
  final String? label;
  final List<dynamic> data;
  final void Function(dynamic)? onChanged;
  final Widget? icon;
  final bool isRequired;
  final bool isMultiSelect;
  final bool ignoring;
  final String? ignoringMessage;
  final Function(dynamic)? itemModelAsName;
  final Function(dynamic)? multiItemsAsName;

  const BaseSearchDropDown(
      {super.key,
      required this.onChanged,
      required this.data,
      required this.label,
      required this.selectedValue,
      this.isRequired = true,
      this.isMultiSelect = false,
      this.ignoring = false,
      this.itemModelAsName,
      this.multiItemsAsName,
      this.ignoringMessage,
      this.icon});

  @override
  Widget build(BuildContext context) {
    final isDropdownOpen = useState(false);

    return Material(
      borderRadius: isDropdownOpen.value
          ? const BorderRadius.only(topLeft: _radius, topRight: _radius)
          : BorderRadius.circular(12),
      // color: Colors.transparent,
      elevation: 0,
      color: Colors.transparent,
      // ColorManager.backgroundColor,
      child: isMultiSelect
          ? _multiSelect(context, isDropdownOpen: isDropdownOpen)
          : _singleSelect(context, isDropdownOpen: isDropdownOpen),
    );
  }

  Widget _singleSelect(BuildContext context,
      {required ValueNotifier<bool> isDropdownOpen}) {
    final local = context.tr;

    return Stack(
      children: [
        DropdownSearch(
          onBeforePopupOpening: (controller) {
            isDropdownOpen.value = true;
            return Future.value(true);
          },
          onBeforeChange: (val, value) {
            isDropdownOpen.value = false;
            return Future.value(true);
          },
          compareFn: (item1, item2) => item1 == item2,
          autoValidateMode: AutovalidateMode.onUserInteraction,
          validator: (value) {
            if (value == null && selectedValue == null && isRequired) {
              return '${local.pleaseSelect} $label';
            }
            return null;
          },
          selectedItem: selectedValue,
          popupProps: PopupProps.modalBottomSheet(
            onDismissed: () {
              isDropdownOpen.value = false;
            },
            modalBottomSheetProps: ModalBottomSheetProps(
              backgroundColor: context.appTheme.cardColor,
              shape: RoundedRectangleBorder(
                borderRadius: !isDropdownOpen.value
                    ? const BorderRadius.only(
                        topLeft: _radius,
                        topRight: _radius,
                      )
                    : BorderRadius.circular(12),
              ),
            ),
            emptyBuilder: (context, searchEntry) {
              return Center(
                child: Text(
                  ignoringMessage ?? local.noDataFound,
                  style: context.whiteLabelLarge,
                ),
              );
            },
            itemBuilder: (context, data, _, __) {
              final isSelected = selectedValue == data;

              return ListTile(
                selectedColor: ColorManager.primaryColor,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppSpaces.mediumPadding,
                ),
                title: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.circle,
                          color: isSelected
                              ? ColorManager.primaryColor
                              : Colors.grey,
                          size: 12,
                        ),
                        context.mediumGap,
                        Expanded(
                          child: Text(
                              itemModelAsName != null
                                  ? itemModelAsName!(data)
                                  : data.toString(),
                              style: context.whiteSubTitle.copyWith(
                                color: Colors.white,
                              )),
                        ),
                      ],
                    ),
                    context.smallGap,
                    const Divider(
                      thickness: .2,
                    ),
                  ],
                ),
              ).paddingSymmetric(
                horizontal: AppSpaces.mediumPadding,
                vertical: AppSpaces.smallPadding,
              );
            },
            // isFilterOnline: false,
            showSelectedItems: false,
            searchFieldProps: TextFieldProps(
              style: context.whiteLabelLarge,
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.mediumPadding),
                hintText: local.search,
                labelStyle: context.whiteLabelLarge,
                helperStyle: context.whiteLabelLarge,
              ),
            ),
            showSearchBox: true,
          ),
          dropdownBuilder: (context, value) {
            if (value == null && selectedValue == null) {
              return Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  '${local.select} $label',
                  style: context.whiteLabelLarge,
                ),
              );
            }

            return Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                itemModelAsName != null
                    ? itemModelAsName!(value)
                    : '${value ?? selectedValue ?? '${local.select} $label'}',
                style: context.whiteLabelLarge,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            );
          },
          decoratorProps: DropDownDecoratorProps(
            decoration: InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                vertical: 12,
                horizontal: icon == null ? 16 : 0,
              ),
              icon: icon,
              border: InputBorder.none,
              labelText: selectedValue != null ? label : null,
              labelStyle: context.whiteLabelMedium.copyWith(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              filled: true,
              fillColor: Colors.transparent,
            ),
          ),
          items: (filter, loadProps) => data,
          onChanged: onChanged,
        ),
        Positioned(
          left: context.isEng ? null : 4,
          top: context.isEng ? null : 4,
          right: context.isEng ? 4 : null,
          bottom: context.isEng ? 4 : null,
          child: const Padding(
            padding: EdgeInsets.all(8.0),
            child: Icon(
              Icons.arrow_drop_down,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _multiSelect(BuildContext context,
      {required ValueNotifier<bool> isDropdownOpen}) {
    final local = context.tr;

    return Stack(
      children: [
        DropdownSearch.multiSelection(
          onBeforePopupOpening: (controller) {
            isDropdownOpen.value = true;
            return Future.value(true);
          },
          onBeforeChange: (val, value) {
            isDropdownOpen.value = false;
            return Future.value(true);
          },
          autoValidateMode: AutovalidateMode.onUserInteraction,
          validator: (value) {
            if (value == null && selectedValue == null && isRequired) {
              return '${local.pleaseSelect} $label';
            }
            return null;
          },
          popupProps: PopupPropsMultiSelection.modalBottomSheet(
            onDismissed: () {
              isDropdownOpen.value = false;
            },
            // menuProps: MenuProps(
            //   elevation: 2,
            //   backgroundColor: context.appTheme.cardColor,
            //   borderRadius: !isDropdownOpen.value
            //       ? const BorderRadius.only(
            //           bottomLeft: _radius,
            //           bottomRight: _radius,
            //         )
            //       : BorderRadius.circular(12),
            // ),
            itemBuilder: (context, data, _, isSelected) {
              return Column(
                children: [
                  ListTile(
                    selected: isSelected,
                    title: Text(
                      itemModelAsName != null
                          ? itemModelAsName!(data)
                          : data.toString(),
                      style: context.whiteSubTitle.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const Divider(
                    thickness: .4,
                  ),
                ],
              );
            },
            // isFilterOnline: false,
            showSelectedItems: false,
            searchFieldProps: TextFieldProps(
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: local.search,
                contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.smallPadding),
              ),
            ),
            showSearchBox: true,
          ),
          dropdownBuilder: (context, value) {
            if (selectedValue == null || selectedValue.isEmpty) {
              return Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  '${local.select} $label',
                  style: context.whiteLabelLarge,
                ),
              );
            }
            return Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                multiItemsAsName != null ? multiItemsAsName!(value) : '$value',
                style: context.whiteLabelLarge,
              ),
            );
          },
          compareFn: (item1, item2) => item1 == item2,
          decoratorProps: DropDownDecoratorProps(
            decoration: InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                vertical: 12,
                horizontal: icon == null ? 16 : 0,
              ),
              icon: icon,
              border: InputBorder.none,
              labelText: selectedValue != null ? label : null,
              labelStyle: context.whiteLabelMedium.copyWith(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              filled: true,
              fillColor: Colors.transparent,
            ),
          ),
          items: (filter, loadProps) => data,
          onChanged: onChanged,
        ),
        Positioned(
          left: context.isEng ? null : 4,
          top: context.isEng ? null : 4,
          right: context.isEng ? 4 : null,
          bottom: context.isEng ? 4 : null,
          child: const Padding(
            padding: EdgeInsets.all(8.0),
            child: Icon(
              Icons.arrow_drop_down,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }
}
