import 'package:admin_dubai/generated/l10n.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../bloc/other_settings_bloc.dart';
import '../../../../models/price_plan_model.dart';
import '../../../../response/price_plan_response.dart';
import 'price_plan_form.dart';

class PricePlans extends StatefulWidget {
  const PricePlans({super.key});

  @override
  _PricePlansState createState() => _PricePlansState();
}

class _PricePlansState extends State<PricePlans> {
  int pagenumber = 1;
  List<PricePlanModel> results = [];

  TextEditingController searchController = TextEditingController();

  Widget _nodatafound(String message) {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget listPricePlans(PricePlanResponse data) {
    return results.isNotEmpty
        ? Expanded(
            child: ListView.builder(
            shrinkWrap: true,
            itemBuilder: (BuildContext ctxt, int index) {
              return Container(
                  padding: const EdgeInsets.only(
                      top: 10, bottom: 5, right: 20, left: 20),
                  child: GestureDetector(
                    onTap: () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return PricePlanForm(
                          pricePlan: results[index],
                        );
                      })).then((value) {
                        results.clear();
                        othersettingsbloc.pricePlans.sink.add(null);
                        othersettingsbloc.getPricePlans(0, 1000, "");
                      });
                    },
                    child: Container(
                        padding: const EdgeInsets.only(
                            top: 15, bottom: 15, right: 20, left: 20),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: Colors.grey[200]!)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      width: 50,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        color: GlobalColors.primaryColor,
                                        borderRadius: BorderRadius.circular(25),
                                      ),
                                      child: const Center(
                                        child: Icon(
                                          Icons.payment,
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 15),
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        results[index].name != null
                                            ? Text(
                                                results[index].name!,
                                                style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 16),
                                              )
                                            : Container(),
                                        const SizedBox(height: 5),
                                        Text(
                                          'EN: ${results[index].nameEn ?? "N/A"}',
                                          style: const TextStyle(
                                              color: Colors.grey, fontSize: 12),
                                        ),
                                        Text(
                                          'AR: ${results[index].nameAr ?? "N/A"}',
                                          style: const TextStyle(
                                              color: Colors.grey, fontSize: 12),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                const Icon(Icons.keyboard_arrow_right)
                              ],
                            ),
                            if (results[index].data != null && results[index].data!.isNotEmpty) ...[
                              const SizedBox(height: 10),
                              const Divider(),
                              const SizedBox(height: 5),
                              Text(
                                '${results[index].data!.length} Payment Plans',
                                style: const TextStyle(
                                    color: Colors.blue,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500),
                              ),
                            ],
                          ],
                        )),
                  ));
            },
            itemCount: results.length,
          ))
        : _nodatafound('No Price Plans to show');
  }

  @override
  void initState() {
    super.initState();
    results.clear();
    othersettingsbloc.pricePlans.sink.add(null);
    othersettingsbloc.getPricePlans(0, 1000, "");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: const Text('Price Plans'),
      ),
      body: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        const SizedBox(height: 20),
        const SizedBox(height: 10),
        Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: Container(
            height: 40,
            decoration: BoxDecoration(
                color: const Color(0xffF1F1F1),
                borderRadius: BorderRadius.circular(3)),
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(5)),
              ),
              child: TextFormField(
                controller: searchController,
                textInputAction: TextInputAction.search,
                onFieldSubmitted: (value) {
                  othersettingsbloc.pricePlans.sink.add(null);
                  othersettingsbloc.getPricePlans(0, 1000, value);
                },
                decoration: InputDecoration(
                  prefixIcon: const Icon(
                    Icons.search,
                    color: Color(0xff8B959E),
                  ),
                  contentPadding:
                      const EdgeInsets.only(left: 20, right: 20, top: 5),
                  hintText: S.of(context).Search,
                  hintStyle:
                      const TextStyle(color: Color(0xff8B959E), fontSize: 13),
                  border: InputBorder.none,
                ),
              ),
            ),
          ),
        ),
        StreamBuilder<PricePlanResponse?>(
          stream: othersettingsbloc.pricePlans.stream,
          builder: (context, AsyncSnapshot<PricePlanResponse?> snapshot) {
            if (snapshot.hasData &&
                snapshot.connectionState != ConnectionState.waiting) {
              if (snapshot.data!.code != 1) {
                snackbar(snapshot.data!.msg!);
                return const SizedBox();
              }
              results.clear();
              results.addAll(snapshot.data!.results);
              return listPricePlans(snapshot.data!);
            } else {
              return const Center(child: ADCircularProgressIndicator());
            }
          },
        )
      ]),
      bottomNavigationBar: Container(
          padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
          child: GestureDetector(
              onTap: () async {
                Navigator.push(context, MaterialPageRoute(builder: (context) {
                  return const PricePlanForm();
                })).then((value) {
                  results.clear();
                  othersettingsbloc.getPricePlans(0, 1000, "");
                });
              },
              child: Container(
                height: 50,
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                    color: GlobalColors.primaryColor,
                    borderRadius: BorderRadius.circular(5)),
                child: Container(
                    padding: const EdgeInsets.all(10),
                    child: const Center(
                        child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.add, color: Colors.white),
                        Text(
                          'Add New Price Plan',
                          style: TextStyle(color: Colors.white),
                        )
                      ],
                    ))),
              ))),
    );
  }
}
