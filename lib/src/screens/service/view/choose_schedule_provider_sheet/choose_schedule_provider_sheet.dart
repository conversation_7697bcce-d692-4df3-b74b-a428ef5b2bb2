import 'dart:math';

import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/calendar_picker/calendar_picker.dart';
import 'package:barber_app/src/core/shared/widgets/lists/base_list.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/auth/models/provider_model.dart';
import 'package:barber_app/src/screens/auth/providers/auth_providers.dart';
import 'package:barber_app/src/screens/order/providers/order.providers.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/utils/calculate_distance.dart';
import '../../../auth/models/user_model.dart';
import '../../../cart/view/address_screen/choose_address_screen.dart';
import '../../../order/models/order.model.dart';
import '../../../setting/providers/setting_providers.dart';
import '../provider_details_screen/choose_map.screen.dart';

class ChooseScheduleProviderSheet extends HookConsumerWidget {
  final ProductModel service;

  const ChooseScheduleProviderSheet({
    super.key,
    required this.service,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDayValue = useState<DateTime>(DateTime.now());
    final selectedTimeValue = useState<String>('');

    final orderController = ref.watch(orderControllerNotifierProvider);
    final authController = ref.watch(authControllerNotifierProvider);
    final settingsController = ref.watch(settingControllerProvider);
    final selectedLocation = useState<LatLng?>(UserModel.currentUser.latLng);
    final freeDistanceRadius = settingsController.setting.freeDistanceKM;
    final afterFreeDistanceCostByKM = settingsController.setting.kmCost;

    void onSaved(LatLng? userPickedLocation) async {
      final formattedTime = selectedTimeValue.value.split(' ')[0];
      final time = TimeOfDay(
          hour: int.parse(formattedTime.split(':')[0]),
          minute: int.parse(formattedTime.split(':')[1]));

      final formatTimeWithPm = time.format(context);

      final isPM = formatTimeWithPm.contains('م');

      Log.w('FDDDffffD ${formatTimeWithPm}');

      final hour = int.parse(formattedTime.split(':')[0]);
      final minute = int.parse(formattedTime.split(':')[1]);

      final date = selectedDayValue.value.copyWith(
        hour: isPM && hour != 12 ? hour + 12 : (!isPM && hour == 12 ? 0 : hour),
        minute: minute,
      );

      final provider =
          (await authController.getClosestAvailableProviderByLatLng(
        lat: userPickedLocation?.latitude.toString(),
        lng: userPickedLocation?.longitude.toString(),
        date: date,
        serviceDocId: service.documentId,
      ));

      if (provider == null) {
        context.back();
        showToast(context.tr.noProvidersAvailableInYourArea, isError: true);
        return;
      }

      final distance = calculateDistance(provider.latLng, userPickedLocation);

      final deliveryCost = distance <= freeDistanceRadius
          ? 0
          : (distance - freeDistanceRadius) * afterFreeDistanceCostByKM;

      final generatedOrderNumber = Random().nextInt(999999);

      final copiedOrder = OrderModel(
        date: date,
        user: UserModel.currentUser,
        address: UserModel.currentUser.address,
        phone: UserModel.currentUser.phone,
        orderNumber: generatedOrderNumber.toString(),
        location: UserModel.currentUser.latLng,
        provider: provider,
        deliveryCost: deliveryCost,
        totalPrice: service.price + deliveryCost,
        productsQuantity: [
          ProductQuantityModel(
            product: service,
            quantity: 1,
            price: service.price,
            totalPrice: service.price,
          )
        ],
        isService: true,
      );

      ChooseAddressScreen(
        order: copiedOrder,
        total: service.price,
        deliveryCost: 0,
        // deliveryCost: deliveryCost,
      ).navigate;
    }

    return Container(
      padding: const EdgeInsets.all(AppSpaces.screenPadding),
      decoration: const BoxDecoration(
        color: ColorManager.backgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppRadius.radius28),
          topRight: Radius.circular(AppRadius.radius28),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppGaps.gap8,

          // * Service Name
          Text(
            '${context.tr.reserve} ${service.name}',
            style: AppTextStyles.title,
          ),

          AppGaps.gap24,

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${context.tr.sessionTime}:',
                style: AppTextStyles.title,
              ),
              Row(
                children: [
                  const Icon(
                    Icons.access_time,
                    color: ColorManager.lightWhite,
                    size: 16,
                  ),
                  AppGaps.gap4,
                  Text(
                    '${service.sessionTime} ${context.tr.minutes}',
                    style: AppTextStyles.subTitle.copyWith(
                      color: ColorManager.lightWhite,
                    ),
                  ),
                ],
              ),
            ],
          ),

          AppGaps.gap24,

          Text(
            context.tr.day,
            style: AppTextStyles.title,
          ),

          AppGaps.gap12,

          CalendarPickerWidget(selectedDayValue: selectedDayValue),

          AppGaps.gap24,

          Builder(
            builder: (context) {
              // get times from 8 am to 8 pm
              final workTimes = [
                const WorkTimeModel(
                  from: '08:00',
                  to: '16:00',
                ),
              ];

              if (workTimes.isEmpty) {
                return Center(
                  child: Text(
                    context.tr.noTimeSlotsAvailable,
                    style: AppTextStyles.subTitle.copyWith(
                      color: ColorManager.lightWhite,
                    ),
                  ),
                );
              }

              return BaseList.horizontal(
                height: 70.h,
                data: workTimes,
                itemBuilder: (workTime, index) {
                  final timeSlots = getTimeSlots(workTime);

                  return Row(
                    children: timeSlots.map((timeSlot) {
                      final formattedTime = timeSlot.format(context);
                      final isSelected =
                          selectedTimeValue.value == formattedTime;

                      if (index == 0 &&
                          timeSlot == timeSlots.first &&
                          selectedTimeValue.value.isEmpty) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          selectedTimeValue.value = formattedTime;
                        });
                      }

                      return GestureDetector(
                        onTap: () {
                          selectedTimeValue.value = formattedTime;
                        },
                        child: Container(
                          width: 70.w,
                          decoration: BoxDecoration(
                            border: isSelected
                                ? null
                                : Border.all(
                                    color: ColorManager.primaryColor,
                                    width: 1,
                                  ),
                            color:
                                isSelected ? ColorManager.primaryColor : null,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            formattedTime,
                            style: AppTextStyles.subTitle.copyWith(
                              color: isSelected
                                  ? Colors.black
                                  : ColorManager.primaryColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ).paddingAll(AppSpaces.padding8),
                      );
                    }).toList(),
                  );
                },
              );
            },
          ),

          AppGaps.gap24,

          Button(
            isLoading: orderController.isLoading || authController.isLoading,
            label: context.tr.reserveNow,
            textColor: Colors.black,
            isPrefixIcon: true,
            onPressed: () async {
              ChooseMapScreen(
                selectedLocation: selectedLocation,
                startLocation: UserModel.currentUser.city?.latLng,
                locationRadiusInKM: UserModel.currentUser.city?.radius,
                onSaved: (userPickedLocation) {
                  onSaved(
                    userPickedLocation,
                  );
                },
                // startLocation: selectedLocation.value,
                // locationRadiusInKM:
                //     settingsController.setting.locationRadiusInKM,
              ).navigate;
            },
          ),
        ],
      ),
    );
  }
}
