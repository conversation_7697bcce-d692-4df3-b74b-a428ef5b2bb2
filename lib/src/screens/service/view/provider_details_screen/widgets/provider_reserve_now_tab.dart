import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/shared/widgets/calendar_picker/calendar_picker.dart';
import 'package:barber_app/src/core/shared/widgets/lists/base_list.dart';
import 'package:barber_app/src/screens/auth/models/provider_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/theme/color_manager.dart';

class ProviderReserveNowTab extends StatelessWidget {
  final ProviderModel provider;
  final ValueNotifier<DateTime> selectedDayValue;
  final ValueNotifier<String> selectedTimeValue;

  const ProviderReserveNowTab(
      {super.key,
      required this.provider,
      required this.selectedDayValue,
      required this.selectedTimeValue});

  @override
  Widget build(BuildContext context) {
    List<WorkTimeModel> getWorkTimesForSelectedDay() {
      final selectedDayName =
          DateFormat('EEEE', 'en').format(selectedDayValue.value);

      return provider.workTimes
          .where((workTime) => workTime.workDay?.name == selectedDayName)
          .toList();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // * Provider Name
        Text(
          provider.name,
          style: AppTextStyles.title,
        ),

        AppGaps.gap24,

        // * Service Name
        Text(
          provider.services.firstOrNull?.name ?? '',
          style: AppTextStyles.title,
        ),

        AppGaps.gap24,

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${context.tr.sessionTime}:',
              style: AppTextStyles.title,
            ),
            Row(
              children: [
                const Icon(
                  Icons.access_time,
                  color: ColorManager.lightWhite,
                  size: 16,
                ),
                AppGaps.gap4,
                Text(
                  '${provider.services.firstOrNull?.sessionTime} ${context.tr.minutes}',
                  style: AppTextStyles.subTitle.copyWith(
                    color: ColorManager.lightWhite,
                  ),
                ),
              ],
            ),
          ],
        ),

        AppGaps.gap24,

        Container(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          decoration: const BoxDecoration(
            color: ColorManager.black,
            borderRadius: BorderRadius.all(
              Radius.circular(AppRadius.radius20),
            ),
          ),
          child: Column(
            children: [
              Text(
                context.tr.reservationDetails,
                style: AppTextStyles.title,
              ),
              AppGaps.gap24,
              CalendarPickerWidget(selectedDayValue: selectedDayValue),
              AppGaps.gap24,
              Builder(
                builder: (context) {
                  final workTimes = getWorkTimesForSelectedDay();

                  if (workTimes.isEmpty) {
                    return Center(
                      child: Text(
                        context.tr.noTimeSlotsAvailable,
                        style: AppTextStyles.subTitle.copyWith(
                          color: ColorManager.lightWhite,
                        ),
                      ),
                    );
                  }

                  return BaseList.horizontal(
                    height: 70.h,
                    data: workTimes,
                    itemBuilder: (workTime, index) {
                      final timeSlots = getTimeSlots(workTime);

                      return Row(
                        children: timeSlots.map((timeSlot) {
                          final formattedTime = timeSlot.format(context);
                          final isSelected =
                              selectedTimeValue.value == formattedTime;

                          if (index == 0 &&
                              timeSlot == timeSlots.first &&
                              selectedTimeValue.value.isEmpty) {
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              selectedTimeValue.value = formattedTime;
                            });
                          }

                          return GestureDetector(
                            onTap: () {
                              selectedTimeValue.value = formattedTime;
                            },
                            child: Container(
                              width: 70.w,
                              decoration: BoxDecoration(
                                border: isSelected
                                    ? null
                                    : Border.all(
                                        color: ColorManager.primaryColor,
                                        width: 1,
                                      ),
                                color: isSelected
                                    ? ColorManager.primaryColor
                                    : null,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                formattedTime,
                                style: AppTextStyles.subTitle.copyWith(
                                  color: isSelected
                                      ? Colors.black
                                      : ColorManager.primaryColor,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ).paddingAll(AppSpaces.padding8),
                          );
                        }).toList(),
                      );
                    },
                  );
                },
              ),
            ],
          ),
        )
      ],
    );
  }
}
