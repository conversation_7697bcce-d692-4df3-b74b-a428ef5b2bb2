import 'dart:math';

import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:barber_app/src/core/theme/color_manager.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/utils/calculate_distance.dart';
import '../../../../auth/models/user_model.dart';
import '../../../../auth/providers/auth_providers.dart';
import '../../../../cart/view/address_screen/choose_address_screen.dart';
import '../../../../order/models/order.model.dart';
import '../../../../order/providers/order.providers.dart';
import '../../../../setting/providers/setting_providers.dart';
import '../../choose_schedule_provider_sheet/choose_schedule_provider_sheet.dart';
import '../../provider_details_screen/choose_map.screen.dart';

class ServiceBottomNavWidget extends HookConsumerWidget {
  final ProductModel service;

  const ServiceBottomNavWidget({super.key, required this.service});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final orderController = ref.watch(orderControllerNotifierProvider);
    final authController = ref.watch(authControllerNotifierProvider);
    final settingsController = ref.watch(settingControllerProvider);
    final selectedLocation = useState<LatLng?>(UserModel.currentUser.latLng);
    final freeDistanceRadius = settingsController.setting.freeDistanceKM;
    final afterFreeDistanceCostByKM = settingsController.setting.kmCost;

    void onSaved(LatLng? userPickedLocation) async {
      final provider =
          (await authController.getClosestAvailableProviderByLatLng(
        lat: userPickedLocation?.latitude.toString(),
        lng: userPickedLocation?.longitude.toString(),
        serviceDocId: service.documentId,
      ));

      if (provider == null) {
        context.back();
        showToast(context.tr.noProvidersAvailableInYourArea, isError: true);
        return;
      }

      final distance = calculateDistance(provider.latLng, userPickedLocation);

      final deliveryCost = distance <= freeDistanceRadius
          ? 0
          : (distance - freeDistanceRadius) * afterFreeDistanceCostByKM;

      final generatedOrderNumber = Random().nextInt(999999);

      final copiedOrder = OrderModel(
        user: UserModel.currentUser,
        address: UserModel.currentUser.address,
        phone: UserModel.currentUser.phone,
        orderNumber: generatedOrderNumber.toString(),
        location: UserModel.currentUser.latLng,
        provider: provider,
        deliveryCost: deliveryCost,
        totalPrice: service.price + deliveryCost,
        productsQuantity: [
          ProductQuantityModel(
            product: service,
            quantity: 1,
            price: service.price,
            totalPrice: service.price,
          )
        ],
        isService: true,
      );

      ChooseAddressScreen(
        order: copiedOrder,
        total: service.price,
        deliveryCost: deliveryCost,
      ).navigate;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Button(
          label: context.tr.scheduleReservation,
          isOutLine: true,
          textColor: ColorManager.white,
          color: ColorManager.white,
          onPressed: () {
            ChooseMapScreen(
              selectedLocation: selectedLocation,
              startLocation: UserModel.currentUser.city?.latLng,
              locationRadiusInKM: UserModel.currentUser.city?.radius,
              onSaved: (userPickedLocation) async {
                final provider =
                    await authController.getClosestAvailableProviderByLatLng(
                  lat: userPickedLocation?.latitude.toString(),
                  lng: userPickedLocation?.longitude.toString(),
                  serviceDocId: service.documentId,
                );

                if (provider == null || provider.availableTimesByDay.isEmpty) {
                  context.back();
                  showToast(context.tr.noProvidersAvailableInYourArea,
                      isError: true);
                  return;
                }

                // Show the schedule provider sheet with the provider data
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  builder: (context) {
                    return ChooseScheduleProviderSheet(
                      service: service,
                      provider: provider,
                      selectedLocation: userPickedLocation,
                    );
                  },
                );
              },
            ).navigate;
          },
        ),
        AppGaps.gap16,
        Button(
          isLoading: orderController.isLoading || authController.isLoading,
          label: context.tr.reserveWithClosestPerson,
          textColor: Colors.black,
          onPressed: () async {
            ChooseMapScreen(
              selectedLocation: selectedLocation,
              startLocation: UserModel.currentUser.city?.latLng,
              locationRadiusInKM: UserModel.currentUser.city?.radius,
              onSaved: (userPickedLocation) async {
                onSaved(userPickedLocation);
              },
            ).navigate;
          },
        ),
      ],
    );
  }
}
