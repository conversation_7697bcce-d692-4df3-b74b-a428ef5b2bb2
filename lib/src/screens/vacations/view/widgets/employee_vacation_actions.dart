import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/vacations/models/vacation_model.dart';
import 'package:opti4t_tasks/src/screens/vacations/providers/vacations_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class EmployeeVacationButton extends ConsumerWidget {
  final VacationModel vacation;

  const EmployeeVacationButton({super.key, required this.vacation});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const CircleAvatar(
            backgroundColor: ColorManager.errorColor,
            child: Icon(
              Icons.delete,
              color: Colors.white,
            ))
        .onTapWithRipple(() => showDialog(
            context: context,
            builder: (_) => DeleteVacationDialog(
                  vacation: vacation,
                )))
        .paddingOnly(
            left: context.isAppEnglish ? 10 : 0,
            right: context.isAppEnglish ? 0 : 10);
  }
}

class DeleteVacationDialog extends HookConsumerWidget {
  final VacationModel vacation;

  const DeleteVacationDialog({
    super.key,
    required this.vacation,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final vacationController =
        ref.watch(vacationControllerNotifierProvider(context));

    void deleteVacation() async {
      await vacationController.deleteVacation(vacation: vacation);
    }

    if (vacationController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    final title = context.tr.deleteVacation;

    final description = context.tr.areYouSureYouWantToDeleteThisVacation;

    final buttonLabel = context.tr.delete;

    return Dialog(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              child: Icon(
                Icons.playlist_add_check_outlined,
                color: Colors.white,
              ),
            ),
            context.mediumGap,
            Text(
              title,
              style: context.whiteTitle,
            ),
          ],
        ),

        context.largeGap,

        //? Are you sure you want to delete this vacation?
        Text(
          description,
          style: context.whiteSubTitle,
        ),

        context.xLargeGap,

        Row(
          children: [
            //? Cancel Button
            Expanded(
              child: TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(context.tr.cancel, style: context.whiteSubTitle),
              ),
            ),

            context.mediumGap,

            Expanded(
                flex: 2,
                child: Button(
                  label: buttonLabel,
                  onPressed: deleteVacation,
                  textColor: Colors.white,
                )),
          ],
        ).sized(
          height: 45,
        )
      ],
    ).paddingAll(AppSpaces.largePadding - 4));
  }
}
