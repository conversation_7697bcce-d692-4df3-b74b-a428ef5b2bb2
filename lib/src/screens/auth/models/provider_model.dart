import 'package:barber_app/src/core/shared/models/base_media.model.dart';
import 'package:barber_app/src/screens/auth/models/city_model.dart';
import 'package:barber_app/src/screens/order/models/order.model.dart';
import 'package:barber_app/src/screens/product/models/product.model.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

List<TimeOfDay> generateTimeSlots(TimeOfDay start, TimeOfDay end) {
  List<TimeOfDay> slots = [];
  TimeOfDay current = start;
  while (current.hour < end.hour ||
      (current.hour == end.hour && current.minute < end.minute)) {
    slots.add(current);
    current = TimeOfDay(
      hour: current.minute + 15 >= 60 ? current.hour + 1 : current.hour,
      minute: (current.minute + 15) % 60,
    );
  }
  return slots;
}

List<TimeOfDay> getTimeSlots(WorkTimeModel workTime) {
  final from = TimeOfDay(
    hour: int.parse(workTime.from.split(':')[0]),
    minute: int.parse(workTime.from.split(':')[1]),
  );
  final to = TimeOfDay(
    hour: int.parse(workTime.to.split(':')[0]),
    minute: int.parse(workTime.to.split(':')[1]),
  );
  return generateTimeSlots(from, to);
}

class AvailableProviderTimeModel {
  final int id;
  final String documentId;
  final String name;
  final String address;
  final DateTime datetime;

  const AvailableProviderTimeModel({
    required this.id,
    required this.documentId,
    required this.name,
    required this.address,
    required this.datetime,
  });

  factory AvailableProviderTimeModel.fromJson(Map<String, dynamic> json) {
    return AvailableProviderTimeModel(
      id: json['id'] ?? 0,
      documentId: json['documentId'] ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      datetime: DateTime.parse(json['datetime']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'documentId': documentId,
      'name': name,
      'address': address,
      'datetime': datetime.toIso8601String(),
    };
  }
}

class AvailableTimeSlotModel {
  final String time;
  final List<AvailableProviderTimeModel> availableProviders;

  const AvailableTimeSlotModel({
    required this.time,
    required this.availableProviders,
  });

  factory AvailableTimeSlotModel.fromJson(Map<String, dynamic> json) {
    return AvailableTimeSlotModel(
      time: json['time'] ?? '',
      availableProviders: json['availableProviders'] == null
          ? []
          : (json['availableProviders'] as List<dynamic>)
              .map((provider) => AvailableProviderTimeModel.fromJson(provider))
              .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'time': time,
      'availableProviders': availableProviders.map((provider) => provider.toJson()).toList(),
    };
  }

  // Helper method to get the first available provider
  AvailableProviderTimeModel? get firstAvailableProvider {
    return availableProviders.isNotEmpty ? availableProviders.first : null;
  }

  // Helper method to check if this time slot has available providers
  bool get hasAvailableProviders => availableProviders.isNotEmpty;
}

class AvailableTimeModel {
  final String time;
  final DateTime datetime;

  const AvailableTimeModel({
    required this.time,
    required this.datetime,
  });

  factory AvailableTimeModel.fromJson(Map<String, dynamic> json) {
    return AvailableTimeModel(
      time: json['time'] ?? '',
      datetime: DateTime.parse(json['datetime']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'time': time,
      'datetime': datetime.toIso8601String(),
    };
  }
}

class ProviderModel {
  final int id;
  final String documentId;
  final String name;
  final String phone;
  final String address;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime publishedAt;
  final String bio;
  final num? providerPercent;
  final BaseMediaModel? image;
  final CityModel? city;
  final List<OrderModel> orders;
  final List<WorkTimeModel> workTimes;
  final List<ProductModel> services;
  final LatLng? latLng;
  final List<AvailableTimeModel> availableTimes;
  final Map<String, List<AvailableTimeSlotModel>> availableTimesByDay;

  const ProviderModel({
    required this.id,
    required this.documentId,
    required this.name,
    required this.phone,
    required this.address,
    required this.createdAt,
    required this.updatedAt,
    required this.publishedAt,
    required this.bio,
    this.providerPercent,
    this.image,
    this.latLng,
    this.city,
    this.orders = const [],
    this.workTimes = const [],
    this.services = const [],
    this.availableTimes = const [],
    this.availableTimesByDay = const {},
  });

  factory ProviderModel.fromJson(Map<String, dynamic> json) {
    return ProviderModel(
      id: json['id'],
      documentId: json['documentId'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      publishedAt: DateTime.parse(json['publishedAt']),
      bio: json['bio'] ?? '',
      providerPercent: json['provider_percent'],
      image:
          json['image'] == null ? null : BaseMediaModel.fromJson(json['image']),
      city: json['city'] == null ? null : CityModel.fromJson(json['city']),
      orders: json['orders'] == null
          ? []
          : (json['orders'] as List<dynamic>)
              .map((order) => OrderModel.fromJson(order))
              .toList(),
      workTimes: json['work_times'] == null
          ? []
          : (json['work_times'] as List<dynamic>)
              .map((workTime) => WorkTimeModel.fromJson(workTime))
              .toList(),
      services: json['services'] == null
          ? []
          : (json['services'] as List<dynamic>)
              .map((service) => ProductModel.fromJson(service['service']))
              .toList(),
      latLng: json['lat'] != null && json['lng'] != null
          ? LatLng(
              double.tryParse(json['lat'].toString()) ?? 0,
              double.tryParse(json['lng'].toString()) ?? 0,
            )
          : null,
      availableTimes: json['availableTimes'] == null
          ? []
          : (json['availableTimes'] as List<dynamic>)
              .map((time) => AvailableTimeModel.fromJson(time))
              .toList(),
      availableTimesByDay: json['availableTimesByDay'] == null
          ? {}
          : (json['availableTimesByDay'] as Map<String, dynamic>).map(
              (day, timeSlots) => MapEntry(
                day,
                (timeSlots as List<dynamic>)
                    .where((timeSlot) => timeSlot != null && timeSlot is Map<String, dynamic> && timeSlot.isNotEmpty)
                    .map((timeSlot) => AvailableTimeSlotModel.fromJson(timeSlot))
                    .toList(),
              ),
            ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'documentId': documentId,
      'name': name,
      'phone': phone,
      'address': address,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'publishedAt': publishedAt.toIso8601String(),
      'bio': bio,
      'provider_percent': providerPercent,
      'image': image?.toJson(),
      'city': city?.toJson(),
      'orders': orders,
      'work_times': workTimes.map((workTime) => workTime.toJson()).toList(),
      'services': services.map((service) => service.toJson()).toList(),
      'availableTimes': availableTimes.map((time) => time.toJson()).toList(),
      'availableTimesByDay': availableTimesByDay.map(
        (day, timeSlots) => MapEntry(
          day,
          timeSlots.map((timeSlot) => timeSlot.toJson()).toList(),
        ),
      ),
      if (latLng != null) ...{
        'lat': latLng!.latitude.toString(),
        'lng': latLng!.longitude.toString(),
      },
    };
  }

  factory ProviderModel.empty() {
    return ProviderModel(
      id: 0,
      documentId: '',
      name: '',
      phone: '',
      address: '',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      publishedAt: DateTime.now(),
      bio: '',
      providerPercent: null,
      image: BaseMediaModel.empty(),
      city: CityModel.empty(),
      orders: [],
      workTimes: [],
      services: [],
      latLng: null,
      availableTimes: [],
      availableTimesByDay: {},
    );
  }
}

class WorkTimeModel {
  final int? id;
  final String from;
  final String to;
  final String day;
  final WorkDayModel? workDay;

  const WorkTimeModel({
    this.id,
    this.from = '',
    this.to = '',
    this.day = '',
    this.workDay,
  });

  factory WorkTimeModel.fromJson(Map<String, dynamic> json) {
    return WorkTimeModel(
      id: json['id'],
      from: json['from'] ?? '',
      to: json['to'] ?? '',
      day: json['day'] ?? '',
      workDay: json['work_day'] == null
          ? null
          : WorkDayModel.fromJson(json['work_day']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'from': from,
      'to': to,
      'day': day,
      'work_day': workDay?.toJson(),
    };
  }
}

class WorkDayModel {
  final int id;
  final String documentId;
  final String name;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime publishedAt;

  const WorkDayModel({
    required this.id,
    required this.documentId,
    required this.name,
    required this.createdAt,
    required this.updatedAt,
    required this.publishedAt,
  });

  factory WorkDayModel.fromJson(Map<String, dynamic> json) {
    return WorkDayModel(
      id: json['id'],
      documentId: json['documentId'] ?? '',
      name: json['name'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      publishedAt: DateTime.parse(json['publishedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'documentId': documentId,
      'name': name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'publishedAt': publishedAt.toIso8601String(),
    };
  }
}
