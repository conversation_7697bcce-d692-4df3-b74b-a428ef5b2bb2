import 'package:flutter/material.dart';
import 'package:fstore/models/entities/category.dart';
import 'package:fstore/models/product_model.dart';
import 'package:fstore/modules/dynamic_layout/config/category_config.dart';

import '../../../../widgets/common/flux_image.dart';
import '../common_item_extension.dart';

class AccessoriesCategoryImageItem extends StatelessWidget {
  final Category cat;
  final products;
  final width;
  final height;
  final CommonItemConfig commonConfig;

  const AccessoriesCategoryImageItem({
    required this.cat,
    this.products,
    this.width,
    this.height,
    required this.commonConfig,
  });

  @override
  Widget build(BuildContext context) {
    const height = 220.0;

    const radius = 0.0;

    return ClipRRect(
      borderRadius: BorderRadius.circular(radius),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: GestureDetector(
          onTap: () {
            ProductModel.showList(
              cateName: cat.name,
              cateId: cat.id,
            );
          },
          child: Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(radius),
                child: FluxImage(
                  imageUrl: cat.image!,
                  height: height,
                  width: MediaQuery.sizeOf(context).width,
                  fit: commonConfig.boxFit,
                ),
              ),
              Container(
                width: double.infinity,
                height: height,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(radius),
                  color: Colors.black.withOpacity(0.5),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      cat.name ?? '',
                      style: const TextStyle(color: Colors.white, fontSize: 30),
                      textAlign: TextAlign.center,
                    ),
                    const Divider(
                      color: Colors.white,
                      thickness: 2,
                      indent: 100,
                      endIndent: 100,
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
