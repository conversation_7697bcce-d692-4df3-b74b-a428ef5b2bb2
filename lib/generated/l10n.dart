// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `تسجيل الدخول`
  String get login {
    return Intl.message('تسجيل الدخول', name: 'login', desc: '', args: []);
  }

  /// `انشاء حساب`
  String get register {
    return Intl.message('انشاء حساب', name: 'register', desc: '', args: []);
  }

  /// `البريد الإلكتروني`
  String get email {
    return Intl.message('البريد الإلكتروني', name: 'email', desc: '', args: []);
  }

  /// `ليس لديك حساب؟`
  String get dontHaveAnAccount {
    return Intl.message(
      'ليس لديك حساب؟',
      name: 'dontHaveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `يرجى اختيار مدينتك`
  String get pleaseChooseYourCity {
    return Intl.message(
      'يرجى اختيار مدينتك',
      name: 'pleaseChooseYourCity',
      desc: '',
      args: [],
    );
  }

  /// `هل لديك حساب؟`
  String get haveAnAccount {
    return Intl.message(
      'هل لديك حساب؟',
      name: 'haveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `كلمة السر`
  String get password {
    return Intl.message('كلمة السر', name: 'password', desc: '', args: []);
  }

  /// `تأكيد كلمة السر`
  String get confirmPassword {
    return Intl.message(
      'تأكيد كلمة السر',
      name: 'confirmPassword',
      desc: '',
      args: [],
    );
  }

  /// `هل نسيت كلمة السر؟`
  String get forgotPassword {
    return Intl.message(
      'هل نسيت كلمة السر؟',
      name: 'forgotPassword',
      desc: '',
      args: [],
    );
  }

  /// `ذكر`
  String get male {
    return Intl.message('ذكر', name: 'male', desc: '', args: []);
  }

  /// `أنثى`
  String get female {
    return Intl.message('أنثى', name: 'female', desc: '', args: []);
  }

  /// `إعادة تعيين كلمة السر`
  String get resetPassword {
    return Intl.message(
      'إعادة تعيين كلمة السر',
      name: 'resetPassword',
      desc: '',
      args: [],
    );
  }

  /// `رقم الهاتف`
  String get mobileNumber {
    return Intl.message('رقم الهاتف', name: 'mobileNumber', desc: '', args: []);
  }

  /// `المدينة`
  String get city {
    return Intl.message('المدينة', name: 'city', desc: '', args: []);
  }

  /// `سجل الدخول بحسابك الآن!`
  String get loginWithYourAccountNow {
    return Intl.message(
      'سجل الدخول بحسابك الآن!',
      name: 'loginWithYourAccountNow',
      desc: '',
      args: [],
    );
  }

  /// `سجل بحسابك الآن!`
  String get registerWithYourAccountNow {
    return Intl.message(
      'سجل بحسابك الآن!',
      name: 'registerWithYourAccountNow',
      desc: '',
      args: [],
    );
  }

  /// `تخطي`
  String get skip {
    return Intl.message('تخطي', name: 'skip', desc: '', args: []);
  }

  /// `المتاجر`
  String get stores {
    return Intl.message('المتاجر', name: 'stores', desc: '', args: []);
  }

  /// `الاسم الكامل`
  String get fullName {
    return Intl.message('الاسم الكامل', name: 'fullName', desc: '', args: []);
  }

  /// `التسجيل كمتجر؟`
  String get registerAsStore {
    return Intl.message(
      'التسجيل كمتجر؟',
      name: 'registerAsStore',
      desc: '',
      args: [],
    );
  }

  /// `التسجيل كطبيب؟`
  String get registerAsDoctor {
    return Intl.message(
      'التسجيل كطبيب؟',
      name: 'registerAsDoctor',
      desc: '',
      args: [],
    );
  }

  /// `الصفحة الرئيسية`
  String get home {
    return Intl.message('الصفحة الرئيسية', name: 'home', desc: '', args: []);
  }

  /// `مقاطع الفيديو`
  String get reels {
    return Intl.message('مقاطع الفيديو', name: 'reels', desc: '', args: []);
  }

  /// `المتاجر`
  String get shops {
    return Intl.message('المتاجر', name: 'shops', desc: '', args: []);
  }

  /// `الأطباء`
  String get doctors {
    return Intl.message('الأطباء', name: 'doctors', desc: '', args: []);
  }

  /// `عرض الكل`
  String get seeAll {
    return Intl.message('عرض الكل', name: 'seeAll', desc: '', args: []);
  }

  /// `مرحبًا، {name}`
  String welcomeWithName(Object name) {
    return Intl.message(
      'مرحبًا، $name',
      name: 'welcomeWithName',
      desc: '',
      args: [name],
    );
  }

  /// `الخدمات`
  String get services {
    return Intl.message('الخدمات', name: 'services', desc: '', args: []);
  }

  /// `المنتجات`
  String get products {
    return Intl.message('المنتجات', name: 'products', desc: '', args: []);
  }

  /// `حول`
  String get about {
    return Intl.message('حول', name: 'about', desc: '', args: []);
  }

  /// `التالي`
  String get next {
    return Intl.message('التالي', name: 'next', desc: '', args: []);
  }

  /// `ابدأ الآن`
  String get startNow {
    return Intl.message('ابدأ الآن', name: 'startNow', desc: '', args: []);
  }

  /// `الوصف`
  String get description {
    return Intl.message('الوصف', name: 'description', desc: '', args: []);
  }

  /// `العنوان`
  String get address {
    return Intl.message('العنوان', name: 'address', desc: '', args: []);
  }

  /// `أدخل`
  String get enter {
    return Intl.message('أدخل', name: 'enter', desc: '', args: []);
  }

  /// `البريد الإلكتروني (اختياري)`
  String get emailOptional {
    return Intl.message(
      'البريد الإلكتروني (اختياري)',
      name: 'emailOptional',
      desc: '',
      args: [],
    );
  }

  /// `حفظ`
  String get save {
    return Intl.message('حفظ', name: 'save', desc: '', args: []);
  }

  /// `إرسال`
  String get submit {
    return Intl.message('إرسال', name: 'submit', desc: '', args: []);
  }

  /// `اختر صورة`
  String get pickImage {
    return Intl.message('اختر صورة', name: 'pickImage', desc: '', args: []);
  }

  /// `تم اختيار الموقع بنجاح`
  String get locationPickedSuccessfully {
    return Intl.message(
      'تم اختيار الموقع بنجاح',
      name: 'locationPickedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `اختر الموقع`
  String get pickLocation {
    return Intl.message(
      'اختر الموقع',
      name: 'pickLocation',
      desc: '',
      args: [],
    );
  }

  /// `اضغط لتحديد الموقع`
  String get tapToSelectLocation {
    return Intl.message(
      'اضغط لتحديد الموقع',
      name: 'tapToSelectLocation',
      desc: '',
      args: [],
    );
  }

  /// `تغيير الموقع`
  String get changeLocation {
    return Intl.message(
      'تغيير الموقع',
      name: 'changeLocation',
      desc: '',
      args: [],
    );
  }

  /// `لم يتم العثور على بيانات`
  String get noDataFound {
    return Intl.message(
      'لم يتم العثور على بيانات',
      name: 'noDataFound',
      desc: '',
      args: [],
    );
  }

  /// `تغيير الاجتماعي`
  String get changeSocial {
    return Intl.message(
      'تغيير الاجتماعي',
      name: 'changeSocial',
      desc: '',
      args: [],
    );
  }

  /// `يرجى إضافة رابط صالح`
  String get pleaseAddValidLink {
    return Intl.message(
      'يرجى إضافة رابط صالح',
      name: 'pleaseAddValidLink',
      desc: '',
      args: [],
    );
  }

  /// `وسائل التواصل الاجتماعي`
  String get socialMedia {
    return Intl.message(
      'وسائل التواصل الاجتماعي',
      name: 'socialMedia',
      desc: '',
      args: [],
    );
  }

  /// `يرجى إضافة موقعك`
  String get pleaseAddYourLocation {
    return Intl.message(
      'يرجى إضافة موقعك',
      name: 'pleaseAddYourLocation',
      desc: '',
      args: [],
    );
  }

  /// `يمكنك أيضًا التسجيل كطبيب من ملفك الشخصي.`
  String get youCanAlsoRegisterAsDoctor {
    return Intl.message(
      'يمكنك أيضًا التسجيل كطبيب من ملفك الشخصي.',
      name: 'youCanAlsoRegisterAsDoctor',
      desc: '',
      args: [],
    );
  }

  /// `يمكنك أيضًا التسجيل كطبيب من ملفك الشخصي.`
  String get youCanAlsoRegisterAsStore {
    return Intl.message(
      'يمكنك أيضًا التسجيل كطبيب من ملفك الشخصي.',
      name: 'youCanAlsoRegisterAsStore',
      desc: '',
      args: [],
    );
  }

  /// `بحث`
  String get search {
    return Intl.message('بحث', name: 'search', desc: '', args: []);
  }

  /// `ابحث عن المنتجات`
  String get searchForProducts {
    return Intl.message(
      'ابحث عن المنتجات',
      name: 'searchForProducts',
      desc: '',
      args: [],
    );
  }

  /// `ابحث عن المتاجر`
  String get searchForStores {
    return Intl.message(
      'ابحث عن المتاجر',
      name: 'searchForStores',
      desc: '',
      args: [],
    );
  }

  /// `ابحث عن الأطباء`
  String get searchForDoctors {
    return Intl.message(
      'ابحث عن الأطباء',
      name: 'searchForDoctors',
      desc: '',
      args: [],
    );
  }

  /// `الأكثر مبيعًا`
  String get bestSellers {
    return Intl.message(
      'الأكثر مبيعًا',
      name: 'bestSellers',
      desc: '',
      args: [],
    );
  }

  /// `أحدث المنتجات`
  String get latestProducts {
    return Intl.message(
      'أحدث المنتجات',
      name: 'latestProducts',
      desc: '',
      args: [],
    );
  }

  /// `الملف الشخصي`
  String get profile {
    return Intl.message('الملف الشخصي', name: 'profile', desc: '', args: []);
  }

  /// `الطلبات`
  String get orders {
    return Intl.message('الطلبات', name: 'orders', desc: '', args: []);
  }

  /// `السلة`
  String get cart {
    return Intl.message('السلة', name: 'cart', desc: '', args: []);
  }

  /// `الكمية`
  String get quantity {
    return Intl.message('الكمية', name: 'quantity', desc: '', args: []);
  }

  /// `تسجيل الخروج`
  String get logout {
    return Intl.message('تسجيل الخروج', name: 'logout', desc: '', args: []);
  }

  /// `أدخل رمز التحقق الذي تلقيته`
  String get enterCodeYouReceived {
    return Intl.message(
      'أدخل رمز التحقق الذي تلقيته',
      name: 'enterCodeYouReceived',
      desc: '',
      args: [],
    );
  }

  /// `أدخل رمز التحقق`
  String get enterYourCode {
    return Intl.message(
      'أدخل رمز التحقق',
      name: 'enterYourCode',
      desc: '',
      args: [],
    );
  }

  /// `رمز التحقق غير صحيح`
  String get verificationCodeIsWrong {
    return Intl.message(
      'رمز التحقق غير صحيح',
      name: 'verificationCodeIsWrong',
      desc: '',
      args: [],
    );
  }

  /// `تم التحقق بنجاح`
  String get verificationSuccessful {
    return Intl.message(
      'تم التحقق بنجاح',
      name: 'verificationSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `من`
  String get from {
    return Intl.message('من', name: 'from', desc: '', args: []);
  }

  /// `إغلاق`
  String get close {
    return Intl.message('إغلاق', name: 'close', desc: '', args: []);
  }

  /// `التقييم`
  String get rate {
    return Intl.message('التقييم', name: 'rate', desc: '', args: []);
  }

  /// `تقييم`
  String get rateOrder {
    return Intl.message('تقييم', name: 'rateOrder', desc: '', args: []);
  }

  /// `اكتب تعليقك`
  String get writeYourComment {
    return Intl.message(
      'اكتب تعليقك',
      name: 'writeYourComment',
      desc: '',
      args: [],
    );
  }

  /// `تطبيق`
  String get apply {
    return Intl.message('تطبيق', name: 'apply', desc: '', args: []);
  }

  /// `ما هو تقييمك ؟`
  String get howWouldYouRate {
    return Intl.message(
      'ما هو تقييمك ؟',
      name: 'howWouldYouRate',
      desc: '',
      args: [],
    );
  }

  /// `اتصل`
  String get call {
    return Intl.message('اتصل', name: 'call', desc: '', args: []);
  }

  /// `إلغاء`
  String get cancel {
    return Intl.message('إلغاء', name: 'cancel', desc: '', args: []);
  }

  /// `سعر المنتج`
  String get productPrice {
    return Intl.message('سعر المنتج', name: 'productPrice', desc: '', args: []);
  }

  /// `تكلفة التوصيل`
  String get deliveryCost {
    return Intl.message(
      'تكلفة التوصيل',
      name: 'deliveryCost',
      desc: '',
      args: [],
    );
  }

  /// `وقت توصيل المنتج`
  String get timeForProductDelivery {
    return Intl.message(
      'وقت توصيل المنتج',
      name: 'timeForProductDelivery',
      desc: '',
      args: [],
    );
  }

  /// `شكرًا لتقييمك`
  String get thankYouForYourRating {
    return Intl.message(
      'شكرًا لتقييمك',
      name: 'thankYouForYourRating',
      desc: '',
      args: [],
    );
  }

  /// `التكلفة الكلية`
  String get totalPrice {
    return Intl.message(
      'التكلفة الكلية',
      name: 'totalPrice',
      desc: '',
      args: [],
    );
  }

  /// `أضف للسلة`
  String get addToCart {
    return Intl.message('أضف للسلة', name: 'addToCart', desc: '', args: []);
  }

  /// `كود الخصم`
  String get promo {
    return Intl.message('كود الخصم', name: 'promo', desc: '', args: []);
  }

  /// `قيمة المشتريات`
  String get totalPurchases {
    return Intl.message(
      'قيمة المشتريات',
      name: 'totalPurchases',
      desc: '',
      args: [],
    );
  }

  /// `الإجمالي`
  String get total {
    return Intl.message('الإجمالي', name: 'total', desc: '', args: []);
  }

  /// `إجمالي الحساب`
  String get totalCalculation {
    return Intl.message(
      'إجمالي الحساب',
      name: 'totalCalculation',
      desc: '',
      args: [],
    );
  }

  /// `تأكيد الطلب`
  String get confirmOrder {
    return Intl.message(
      'تأكيد الطلب',
      name: 'confirmOrder',
      desc: '',
      args: [],
    );
  }

  /// `اطلب الآن`
  String get orderNow {
    return Intl.message('اطلب الآن', name: 'orderNow', desc: '', args: []);
  }

  /// `اكمل التسوق`
  String get continueShopping {
    return Intl.message(
      'اكمل التسوق',
      name: 'continueShopping',
      desc: '',
      args: [],
    );
  }

  /// `السلة فارغة`
  String get cartIsEmpty {
    return Intl.message('السلة فارغة', name: 'cartIsEmpty', desc: '', args: []);
  }

  /// `تمت الإضافة بنجاح`
  String get addedSuccessfully {
    return Intl.message(
      'تمت الإضافة بنجاح',
      name: 'addedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `الكود غير صحيح`
  String get invalidPromo {
    return Intl.message(
      'الكود غير صحيح',
      name: 'invalidPromo',
      desc: '',
      args: [],
    );
  }

  /// `لا يمكن أن يكون فارغًا`
  String get cannotBeEmpty {
    return Intl.message(
      'لا يمكن أن يكون فارغًا',
      name: 'cannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `إزالة`
  String get remove {
    return Intl.message('إزالة', name: 'remove', desc: '', args: []);
  }

  /// `الخصم`
  String get discount {
    return Intl.message('الخصم', name: 'discount', desc: '', args: []);
  }

  /// `السعر`
  String get price {
    return Intl.message('السعر', name: 'price', desc: '', args: []);
  }

  /// `احجز الآن مع أقرب شخص`
  String get reserveWithClosestPerson {
    return Intl.message(
      'احجز الآن مع أقرب شخص',
      name: 'reserveWithClosestPerson',
      desc: '',
      args: [],
    );
  }

  /// `الطلبات فارغة`
  String get ordersAreEmpty {
    return Intl.message(
      'الطلبات فارغة',
      name: 'ordersAreEmpty',
      desc: '',
      args: [],
    );
  }

  /// `الهاتف`
  String get phone {
    return Intl.message('الهاتف', name: 'phone', desc: '', args: []);
  }

  /// `الامن`
  String get safety {
    return Intl.message('الامن', name: 'safety', desc: '', args: []);
  }

  /// `المساعده و الدعم`
  String get support {
    return Intl.message(
      'المساعده و الدعم',
      name: 'support',
      desc: '',
      args: [],
    );
  }

  /// `حول التطبيق`
  String get aboutApp {
    return Intl.message('حول التطبيق', name: 'aboutApp', desc: '', args: []);
  }

  /// `تعديل الملف الشخصي`
  String get updateProfile {
    return Intl.message(
      'تعديل الملف الشخصي',
      name: 'updateProfile',
      desc: '',
      args: [],
    );
  }

  /// `الرجاء تسجيل الدخول أولا`
  String get loginFirstPlease {
    return Intl.message(
      'الرجاء تسجيل الدخول أولا',
      name: 'loginFirstPlease',
      desc: '',
      args: [],
    );
  }

  /// `المتخصصين المتاحين`
  String get availableProvider {
    return Intl.message(
      'المتخصصين المتاحين',
      name: 'availableProvider',
      desc: '',
      args: [],
    );
  }

  /// `تأكيد عنوان الشحن`
  String get confirmShippingAddress {
    return Intl.message(
      'تأكيد عنوان الشحن',
      name: 'confirmShippingAddress',
      desc: '',
      args: [],
    );
  }

  /// `ملاحظة`
  String get note {
    return Intl.message('ملاحظة', name: 'note', desc: '', args: []);
  }

  /// `تم عمل الطلب بنجاح`
  String get orderMadeSuccessfully {
    return Intl.message(
      'تم عمل الطلب بنجاح',
      name: 'orderMadeSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `المنتجات المطلوبة سابقا`
  String get lastProductsOrders {
    return Intl.message(
      'المنتجات المطلوبة سابقا',
      name: 'lastProductsOrders',
      desc: '',
      args: [],
    );
  }

  /// `الطلبات السابقة`
  String get lastOrders {
    return Intl.message(
      'الطلبات السابقة',
      name: 'lastOrders',
      desc: '',
      args: [],
    );
  }

  /// `الطلبات الحالية`
  String get currentOrders {
    return Intl.message(
      'الطلبات الحالية',
      name: 'currentOrders',
      desc: '',
      args: [],
    );
  }

  /// `تأكيد عنوان طلب الخدمة`
  String get confirmServiceAddress {
    return Intl.message(
      'تأكيد عنوان طلب الخدمة',
      name: 'confirmServiceAddress',
      desc: '',
      args: [],
    );
  }

  /// `احجز الآن`
  String get reserveNow {
    return Intl.message('احجز الآن', name: 'reserveNow', desc: '', args: []);
  }

  /// `ادفع الآن`
  String get payNow {
    return Intl.message('ادفع الآن', name: 'payNow', desc: '', args: []);
  }

  /// `يرجى تحديد وقت حجز صحيح`
  String get pleaseSelectValidTime {
    return Intl.message(
      'يرجى تحديد وقت حجز صحيح',
      name: 'pleaseSelectValidTime',
      desc: '',
      args: [],
    );
  }

  /// `مدة الجلسة`
  String get sessionTime {
    return Intl.message('مدة الجلسة', name: 'sessionTime', desc: '', args: []);
  }

  /// `الدفع لتأكيد الحجز`
  String get minCostToPay {
    return Intl.message(
      'الدفع لتأكيد الحجز',
      name: 'minCostToPay',
      desc: '',
      args: [],
    );
  }

  /// `التكلفة`
  String get cost {
    return Intl.message('التكلفة', name: 'cost', desc: '', args: []);
  }

  /// `تم تحديث الملف الشخصي بنجاح`
  String get profileUpdatedSuccessfully {
    return Intl.message(
      'تم تحديث الملف الشخصي بنجاح',
      name: 'profileUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `أنت خارج المنطقة المسموح بها`
  String get youAreOutSideTheAvailableArea {
    return Intl.message(
      'أنت خارج المنطقة المسموح بها',
      name: 'youAreOutSideTheAvailableArea',
      desc: '',
      args: [],
    );
  }

  /// `حجز`
  String get reserve {
    return Intl.message('حجز', name: 'reserve', desc: '', args: []);
  }

  /// `لمنطقة`
  String get forCity {
    return Intl.message('لمنطقة', name: 'forCity', desc: '', args: []);
  }

  /// `اليوم`
  String get day {
    return Intl.message('اليوم', name: 'day', desc: '', args: []);
  }

  /// `تفاصيل الحجز المطلوب`
  String get reservationDetails {
    return Intl.message(
      'تفاصيل الحجز المطلوب',
      name: 'reservationDetails',
      desc: '',
      args: [],
    );
  }

  /// `إجمالي تكلفة الخدمة`
  String get totalServiceCost {
    return Intl.message(
      'إجمالي تكلفة الخدمة',
      name: 'totalServiceCost',
      desc: '',
      args: [],
    );
  }

  /// `الساعة`
  String get hour {
    return Intl.message('الساعة', name: 'hour', desc: '', args: []);
  }

  /// `دقيقة`
  String get minutes {
    return Intl.message('دقيقة', name: 'minutes', desc: '', args: []);
  }

  /// `جدولة الحجز`
  String get scheduleReservation {
    return Intl.message(
      'جدولة الحجز',
      name: 'scheduleReservation',
      desc: '',
      args: [],
    );
  }

  /// `لا توجد مواعيد زمنية متاحة`
  String get noTimeSlotsAvailable {
    return Intl.message(
      'لا توجد مواعيد زمنية متاحة',
      name: 'noTimeSlotsAvailable',
      desc: '',
      args: [],
    );
  }

  /// `معلومات مقدم الخدمة`
  String get providerInfo {
    return Intl.message(
      'معلومات مقدم الخدمة',
      name: 'providerInfo',
      desc: '',
      args: [],
    );
  }

  /// `تكلفة الوصول إليك`
  String get deliveryForYouCost {
    return Intl.message(
      'تكلفة الوصول إليك',
      name: 'deliveryForYouCost',
      desc: '',
      args: [],
    );
  }

  /// `النبذة التعريفية`
  String get bio {
    return Intl.message('النبذة التعريفية', name: 'bio', desc: '', args: []);
  }

  /// `تم الحجز بنجاح`
  String get reservedMadeSuccessfully {
    return Intl.message(
      'تم الحجز بنجاح',
      name: 'reservedMadeSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `اختر موقعك`
  String get chooseYourLocation {
    return Intl.message(
      'اختر موقعك',
      name: 'chooseYourLocation',
      desc: '',
      args: [],
    );
  }

  /// `تم الحفظ بنجاح`
  String get savedSuccessfully {
    return Intl.message(
      'تم الحفظ بنجاح',
      name: 'savedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `ادفع`
  String get pay {
    return Intl.message('ادفع', name: 'pay', desc: '', args: []);
  }

  /// `الدفع`
  String get payment {
    return Intl.message('الدفع', name: 'payment', desc: '', args: []);
  }

  /// `لا يوجد مقدمين خدمات متاحين في منطقتك`
  String get noProvidersAvailableInYourArea {
    return Intl.message(
      'لا يوجد مقدمين خدمات متاحين في منطقتك',
      name: 'noProvidersAvailableInYourArea',
      desc: '',
      args: [],
    );
  }

  /// `الاسم`
  String get name {
    return Intl.message('الاسم', name: 'name', desc: '', args: []);
  }

  /// `معلومات المستخدم`
  String get userInfo {
    return Intl.message(
      'معلومات المستخدم',
      name: 'userInfo',
      desc: '',
      args: [],
    );
  }

  /// `تكلفة الخدمة`
  String get serviceCost {
    return Intl.message(
      'تكلفة الخدمة',
      name: 'serviceCost',
      desc: '',
      args: [],
    );
  }

  /// `تفاصيل الطلب`
  String get orderDetails {
    return Intl.message(
      'تفاصيل الطلب',
      name: 'orderDetails',
      desc: '',
      args: [],
    );
  }

  /// `تفاصيل تكلفة الطلب`
  String get orderCostInfo {
    return Intl.message(
      'تفاصيل تكلفة الطلب',
      name: 'orderCostInfo',
      desc: '',
      args: [],
    );
  }

  /// `فشل الدفع، يرجى المحاولة مرة اخرى`
  String get paymentFailed {
    return Intl.message(
      'فشل الدفع، يرجى المحاولة مرة اخرى',
      name: 'paymentFailed',
      desc: '',
      args: [],
    );
  }

  /// `الأوقات المتاحة`
  String get availableTimes {
    return Intl.message(
      'الأوقات المتاحة',
      name: 'availableTimes',
      desc: '',
      args: [],
    );
  }

  /// `يرجى اختيار الوقت`
  String get pleaseSelectTime {
    return Intl.message(
      'يرجى اختيار الوقت',
      name: 'pleaseSelectTime',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
