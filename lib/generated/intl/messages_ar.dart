// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(name) => "مرحبًا، ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "active": MessageLookupByLibrary.simpleMessage("نشط"),
        "addNewTicket":
            MessageLookupByLibrary.simpleMessage("إضافة تذكرة جديدة"),
        "allTickets": MessageLookupByLibrary.simpleMessage("جميع التذاكر"),
        "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
        "archived": MessageLookupByLibrary.simpleMessage("مؤرشف"),
        "areYouSureYouWantToLogout": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد أنك تريد تسجيل الخروج؟"),
        "ascending": MessageLookupByLibrary.simpleMessage("تصاعدي"),
        "attachment": MessageLookupByLibrary.simpleMessage("المرفق"),
        "camera": MessageLookupByLibrary.simpleMessage("الكاميرا"),
        "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
        "changeLanguage": MessageLookupByLibrary.simpleMessage("تغيير اللغة"),
        "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
        "days": MessageLookupByLibrary.simpleMessage("أيام"),
        "descending": MessageLookupByLibrary.simpleMessage("تنازلي"),
        "description": MessageLookupByLibrary.simpleMessage("الوصف"),
        "endDate": MessageLookupByLibrary.simpleMessage("تاريخ النهاية"),
        "english": MessageLookupByLibrary.simpleMessage("English"),
        "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
        "gallery": MessageLookupByLibrary.simpleMessage("المعرض"),
        "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
        "issue": MessageLookupByLibrary.simpleMessage("مشكلة"),
        "issuerEmail":
            MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "issuerName": MessageLookupByLibrary.simpleMessage("اسم المنشئ"),
        "issuerPhone": MessageLookupByLibrary.simpleMessage("الهاتف"),
        "itsGreatToSeeYou":
            MessageLookupByLibrary.simpleMessage("من الرائع رؤيتك"),
        "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
        "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
        "mySubscriptions": MessageLookupByLibrary.simpleMessage("اشتراكاتي"),
        "newReplyOnTicket":
            MessageLookupByLibrary.simpleMessage("رد جديد على التذكرة"),
        "noDataFound": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
        "noRepliesFound":
            MessageLookupByLibrary.simpleMessage("لم يتم العثور على ردود"),
        "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
        "pickImage": MessageLookupByLibrary.simpleMessage("اختر صورة"),
        "recentActiveTickets":
            MessageLookupByLibrary.simpleMessage("التذاكر النشطة الأخيرة"),
        "register": MessageLookupByLibrary.simpleMessage("تسجيل"),
        "remainingMaintenance":
            MessageLookupByLibrary.simpleMessage("الصيانة المتبقية"),
        "repliedOnTheTicket":
            MessageLookupByLibrary.simpleMessage("رد على التذكرة"),
        "replies": MessageLookupByLibrary.simpleMessage("الردود"),
        "reply": MessageLookupByLibrary.simpleMessage("رد"),
        "replyCannotBeEmpty":
            MessageLookupByLibrary.simpleMessage("لا يمكن أن يكون الرد فارغًا"),
        "replySentSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم إرسال الرد بنجاح"),
        "reports": MessageLookupByLibrary.simpleMessage("التقارير"),
        "request": MessageLookupByLibrary.simpleMessage("طلب"),
        "save": MessageLookupByLibrary.simpleMessage("حفظ"),
        "search": MessageLookupByLibrary.simpleMessage("بحث"),
        "sortByDate": MessageLookupByLibrary.simpleMessage("ترتيب حسب التاريخ"),
        "startDate": MessageLookupByLibrary.simpleMessage("تاريخ البداية"),
        "status": MessageLookupByLibrary.simpleMessage("الحالة"),
        "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
        "totalTickets": MessageLookupByLibrary.simpleMessage("إجمالي التذاكر"),
        "username": MessageLookupByLibrary.simpleMessage("اسم المستخدم"),
        "welcomeBack": MessageLookupByLibrary.simpleMessage("مرحبًا بعودتك"),
        "welcomeBackLine":
            MessageLookupByLibrary.simpleMessage("مرحبًا\nبعودتك"),
        "welcomeWithName": m0
      };
}
