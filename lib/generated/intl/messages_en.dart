// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(name) => "مرحبًا، ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about": MessageLookupByLibrary.simpleMessage("حول"),
    "aboutApp": MessageLookupByLibrary.simpleMessage("حول التطبيق"),
    "addToCart": MessageLookupByLibrary.simpleMessage("أضف للسلة"),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تمت الإضافة بنجاح",
    ),
    "address": MessageLookupByLibrary.simpleMessage("العنوان"),
    "apply": MessageLookupByLibrary.simpleMessage("تطبيق"),
    "availableProvider": MessageLookupByLibrary.simpleMessage(
      "المتخصصين المتاحين",
    ),
    "availableTimes": MessageLookupByLibrary.simpleMessage("الأوقات المتاحة"),
    "bestSellers": MessageLookupByLibrary.simpleMessage("الأكثر مبيعًا"),
    "bio": MessageLookupByLibrary.simpleMessage("النبذة التعريفية"),
    "call": MessageLookupByLibrary.simpleMessage("اتصل"),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "cannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون فارغًا",
    ),
    "cart": MessageLookupByLibrary.simpleMessage("السلة"),
    "cartIsEmpty": MessageLookupByLibrary.simpleMessage("السلة فارغة"),
    "changeLocation": MessageLookupByLibrary.simpleMessage("تغيير الموقع"),
    "changeSocial": MessageLookupByLibrary.simpleMessage("تغيير الاجتماعي"),
    "chooseYourLocation": MessageLookupByLibrary.simpleMessage("اختر موقعك"),
    "city": MessageLookupByLibrary.simpleMessage("المدينة"),
    "close": MessageLookupByLibrary.simpleMessage("إغلاق"),
    "confirmOrder": MessageLookupByLibrary.simpleMessage("تأكيد الطلب"),
    "confirmPassword": MessageLookupByLibrary.simpleMessage("تأكيد كلمة السر"),
    "confirmServiceAddress": MessageLookupByLibrary.simpleMessage(
      "تأكيد عنوان طلب الخدمة",
    ),
    "confirmShippingAddress": MessageLookupByLibrary.simpleMessage(
      "تأكيد عنوان الشحن",
    ),
    "continueShopping": MessageLookupByLibrary.simpleMessage("اكمل التسوق"),
    "cost": MessageLookupByLibrary.simpleMessage("التكلفة"),
    "currentOrders": MessageLookupByLibrary.simpleMessage("الطلبات الحالية"),
    "day": MessageLookupByLibrary.simpleMessage("اليوم"),
    "deliveryCost": MessageLookupByLibrary.simpleMessage("تكلفة التوصيل"),
    "deliveryForYouCost": MessageLookupByLibrary.simpleMessage(
      "تكلفة الوصول إليك",
    ),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "discount": MessageLookupByLibrary.simpleMessage("الخصم"),
    "doctors": MessageLookupByLibrary.simpleMessage("الأطباء"),
    "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "emailOptional": MessageLookupByLibrary.simpleMessage(
      "البريد الإلكتروني (اختياري)",
    ),
    "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
    "enterCodeYouReceived": MessageLookupByLibrary.simpleMessage(
      "أدخل رمز التحقق الذي تلقيته",
    ),
    "enterYourCode": MessageLookupByLibrary.simpleMessage("أدخل رمز التحقق"),
    "female": MessageLookupByLibrary.simpleMessage("أنثى"),
    "forCity": MessageLookupByLibrary.simpleMessage("لمنطقة"),
    "forgotPassword": MessageLookupByLibrary.simpleMessage(
      "هل نسيت كلمة السر؟",
    ),
    "from": MessageLookupByLibrary.simpleMessage("من"),
    "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
    "haveAnAccount": MessageLookupByLibrary.simpleMessage("هل لديك حساب؟"),
    "home": MessageLookupByLibrary.simpleMessage("الصفحة الرئيسية"),
    "hour": MessageLookupByLibrary.simpleMessage("الساعة"),
    "howWouldYouRate": MessageLookupByLibrary.simpleMessage("ما هو تقييمك ؟"),
    "invalidPromo": MessageLookupByLibrary.simpleMessage("الكود غير صحيح"),
    "lastOrders": MessageLookupByLibrary.simpleMessage("الطلبات السابقة"),
    "lastProductsOrders": MessageLookupByLibrary.simpleMessage(
      "المنتجات المطلوبة سابقا",
    ),
    "latestProducts": MessageLookupByLibrary.simpleMessage("أحدث المنتجات"),
    "locationPickedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم اختيار الموقع بنجاح",
    ),
    "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "loginFirstPlease": MessageLookupByLibrary.simpleMessage(
      "الرجاء تسجيل الدخول أولا",
    ),
    "loginWithYourAccountNow": MessageLookupByLibrary.simpleMessage(
      "سجل الدخول بحسابك الآن!",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "male": MessageLookupByLibrary.simpleMessage("ذكر"),
    "minCostToPay": MessageLookupByLibrary.simpleMessage("الدفع لتأكيد الحجز"),
    "minutes": MessageLookupByLibrary.simpleMessage("دقيقة"),
    "mobileNumber": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
    "name": MessageLookupByLibrary.simpleMessage("الاسم"),
    "next": MessageLookupByLibrary.simpleMessage("التالي"),
    "noDataFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على بيانات",
    ),
    "noProvidersAvailableInYourArea": MessageLookupByLibrary.simpleMessage(
      "لا يوجد مقدمين خدمات متاحين في منطقتك",
    ),
    "noTimeSlotsAvailable": MessageLookupByLibrary.simpleMessage(
      "لا توجد مواعيد زمنية متاحة",
    ),
    "note": MessageLookupByLibrary.simpleMessage("ملاحظة"),
    "orderCostInfo": MessageLookupByLibrary.simpleMessage("تفاصيل تكلفة الطلب"),
    "orderDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الطلب"),
    "orderMadeSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم عمل الطلب بنجاح",
    ),
    "orderNow": MessageLookupByLibrary.simpleMessage("اطلب الآن"),
    "orders": MessageLookupByLibrary.simpleMessage("الطلبات"),
    "ordersAreEmpty": MessageLookupByLibrary.simpleMessage("الطلبات فارغة"),
    "password": MessageLookupByLibrary.simpleMessage("كلمة السر"),
    "pay": MessageLookupByLibrary.simpleMessage("ادفع"),
    "payNow": MessageLookupByLibrary.simpleMessage("ادفع الآن"),
    "payment": MessageLookupByLibrary.simpleMessage("الدفع"),
    "paymentFailed": MessageLookupByLibrary.simpleMessage(
      "فشل الدفع، يرجى المحاولة مرة اخرى",
    ),
    "phone": MessageLookupByLibrary.simpleMessage("الهاتف"),
    "pickImage": MessageLookupByLibrary.simpleMessage("اختر صورة"),
    "pickLocation": MessageLookupByLibrary.simpleMessage("اختر الموقع"),
    "pleaseAddValidLink": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة رابط صالح",
    ),
    "pleaseAddYourLocation": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة موقعك",
    ),
    "pleaseChooseYourCity": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار مدينتك",
    ),
    "pleaseSelectTime": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار الوقت",
    ),
    "pleaseSelectValidTime": MessageLookupByLibrary.simpleMessage(
      "يرجى تحديد وقت حجز صحيح",
    ),
    "price": MessageLookupByLibrary.simpleMessage("السعر"),
    "productPrice": MessageLookupByLibrary.simpleMessage("سعر المنتج"),
    "products": MessageLookupByLibrary.simpleMessage("المنتجات"),
    "profile": MessageLookupByLibrary.simpleMessage("الملف الشخصي"),
    "profileUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الملف الشخصي بنجاح",
    ),
    "promo": MessageLookupByLibrary.simpleMessage("كود الخصم"),
    "providerInfo": MessageLookupByLibrary.simpleMessage("معلومات مقدم الخدمة"),
    "quantity": MessageLookupByLibrary.simpleMessage("الكمية"),
    "rate": MessageLookupByLibrary.simpleMessage("التقييم"),
    "rateOrder": MessageLookupByLibrary.simpleMessage("تقييم"),
    "reels": MessageLookupByLibrary.simpleMessage("مقاطع الفيديو"),
    "register": MessageLookupByLibrary.simpleMessage("انشاء حساب"),
    "registerAsDoctor": MessageLookupByLibrary.simpleMessage("التسجيل كطبيب؟"),
    "registerAsStore": MessageLookupByLibrary.simpleMessage("التسجيل كمتجر؟"),
    "registerWithYourAccountNow": MessageLookupByLibrary.simpleMessage(
      "سجل بحسابك الآن!",
    ),
    "remove": MessageLookupByLibrary.simpleMessage("إزالة"),
    "reservationDetails": MessageLookupByLibrary.simpleMessage(
      "تفاصيل الحجز المطلوب",
    ),
    "reserve": MessageLookupByLibrary.simpleMessage("حجز"),
    "reserveNow": MessageLookupByLibrary.simpleMessage("احجز الآن"),
    "reserveWithClosestPerson": MessageLookupByLibrary.simpleMessage(
      "احجز الآن مع أقرب شخص",
    ),
    "reservedMadeSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم الحجز بنجاح",
    ),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "إعادة تعيين كلمة السر",
    ),
    "safety": MessageLookupByLibrary.simpleMessage("الامن"),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "savedSuccessfully": MessageLookupByLibrary.simpleMessage("تم الحفظ بنجاح"),
    "scheduleReservation": MessageLookupByLibrary.simpleMessage("جدولة الحجز"),
    "search": MessageLookupByLibrary.simpleMessage("بحث"),
    "searchForDoctors": MessageLookupByLibrary.simpleMessage("ابحث عن الأطباء"),
    "searchForProducts": MessageLookupByLibrary.simpleMessage(
      "ابحث عن المنتجات",
    ),
    "searchForStores": MessageLookupByLibrary.simpleMessage("ابحث عن المتاجر"),
    "seeAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
    "serviceCost": MessageLookupByLibrary.simpleMessage("تكلفة الخدمة"),
    "services": MessageLookupByLibrary.simpleMessage("الخدمات"),
    "sessionTime": MessageLookupByLibrary.simpleMessage("مدة الجلسة"),
    "shops": MessageLookupByLibrary.simpleMessage("المتاجر"),
    "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
    "socialMedia": MessageLookupByLibrary.simpleMessage(
      "وسائل التواصل الاجتماعي",
    ),
    "startNow": MessageLookupByLibrary.simpleMessage("ابدأ الآن"),
    "stores": MessageLookupByLibrary.simpleMessage("المتاجر"),
    "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
    "support": MessageLookupByLibrary.simpleMessage("المساعده و الدعم"),
    "tapToSelectLocation": MessageLookupByLibrary.simpleMessage(
      "اضغط لتحديد الموقع",
    ),
    "thankYouForYourRating": MessageLookupByLibrary.simpleMessage(
      "شكرًا لتقييمك",
    ),
    "timeForProductDelivery": MessageLookupByLibrary.simpleMessage(
      "وقت توصيل المنتج",
    ),
    "total": MessageLookupByLibrary.simpleMessage("الإجمالي"),
    "totalCalculation": MessageLookupByLibrary.simpleMessage("إجمالي الحساب"),
    "totalPrice": MessageLookupByLibrary.simpleMessage("التكلفة الكلية"),
    "totalPurchases": MessageLookupByLibrary.simpleMessage("قيمة المشتريات"),
    "totalServiceCost": MessageLookupByLibrary.simpleMessage(
      "إجمالي تكلفة الخدمة",
    ),
    "updateProfile": MessageLookupByLibrary.simpleMessage("تعديل الملف الشخصي"),
    "userInfo": MessageLookupByLibrary.simpleMessage("معلومات المستخدم"),
    "verificationCodeIsWrong": MessageLookupByLibrary.simpleMessage(
      "رمز التحقق غير صحيح",
    ),
    "verificationSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم التحقق بنجاح",
    ),
    "welcomeWithName": m0,
    "writeYourComment": MessageLookupByLibrary.simpleMessage("اكتب تعليقك"),
    "youAreOutSideTheAvailableArea": MessageLookupByLibrary.simpleMessage(
      "أنت خارج المنطقة المسموح بها",
    ),
    "youCanAlsoRegisterAsDoctor": MessageLookupByLibrary.simpleMessage(
      "يمكنك أيضًا التسجيل كطبيب من ملفك الشخصي.",
    ),
    "youCanAlsoRegisterAsStore": MessageLookupByLibrary.simpleMessage(
      "يمكنك أيضًا التسجيل كطبيب من ملفك الشخصي.",
    ),
  };
}
